import tkinter as tk


def start_timer():
    length = entry.get()

    # Validate the input
    try:
        length = int(length)
        if length <= 0:
            raise ValueError()
    except ValueError:
        entry.config(fg="red")
        return

    entry.delete(0, tk.END)  # Clear the input field

    # Hide the root window
    root.withdraw()

    # Create a new fullscreen window
    window = tk.Toplevel()
    window.attributes('-fullscreen', True)
    window.config(cursor="none")  # Hide the cursor

    # Frame to center the label vertically
    frame = tk.Frame(window)
    frame.pack(expand=True)

    # Label to display the timer status
    timer_label = tk.Label(frame, text="Pradėdami kalbą paspauskite ENTER", font=("Arial", 50))
    timer_label.pack(pady=100)

    # Variable to keep track of the timer state
    timer_running = False
    remaining_time = length
    timer_id = None  # Variable to store the timer ID

    def update_timer(seconds):
        nonlocal timer_running, remaining_time, timer_id

        if seconds <= 0:
            window.after(1, restart_timer)
            timer_running = False
            timer_label.config(text="Ačiū už Jū<PERSON> kalbą!", font=("Arial", 50))
        elif seconds > 0:
            minutes = seconds // 60
            seconds_remaining = seconds % 60
            timer_label.config(text=f"{minutes:02d}:{seconds_remaining:02d}", font=("Arial", 150))
            timer_id = window.after(1000, update_timer, seconds - 1)

    def restart_timer():
        nonlocal remaining_time

        timer_label.config(text="Ačiū už Jūsų kalbą!", font=("Arial", 50))
        window.bind('<Return>', handle_enter)

    def handle_enter(event=None):  # Add a default value for the event parameter
        nonlocal timer_running, remaining_time, timer_id

        if timer_running:
            window.after_cancel(timer_id)  # Cancel the ongoing timer

        remaining_time = length
        update_timer(remaining_time)
        timer_running = True
        
    def handle_esc(event=None):
        nonlocal timer_running, remaining_time, timer_id
        window.after_cancel(timer_id)
        timer_running = False
        timer_label.config(text="Ačiū už Jūsų kalbą!", font=("Arial", 50))


    window.bind('<space>', handle_enter)
    window.bind('<Escape>', handle_esc)
    window.bind('p', lambda event: root.destroy())

    window.focus_set()
    window.grab_set()
    window.mainloop()


# Create the root window
root = tk.Tk()
root.title("MB laikmatis")

# Prompt the user for the length of the timer
length_label = tk.Label(root, text="Įveskite nustatytą laiką (sekundėmis):", font=("Arial", 14))
length_label.pack(pady=10)

entry = tk.Entry(root, font=("Arial", 14))
entry.pack(pady=10)

entry.focus_set()  # Set focus on the entry field

root.bind('<Return>', lambda event: start_timer())
root.bind('p', lambda event: root.destroy())

root.mainloop()


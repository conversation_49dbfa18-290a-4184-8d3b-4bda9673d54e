"""
Demo script to show the difference between old tkinter and modern CustomTkinter
"""
import tkinter as tk
from tkinter import ttk
import customtkinter as ctk

def show_old_style():
    """Show old tkinter style window"""
    root = tk.Tk()
    root.title("Old Tkinter Style")
    root.geometry("600x400")
    
    # Old style widgets
    frame = tk.Frame(root, bg="lightgray")
    frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    tk.Label(frame, text="Excel Duomenų Apdorojimas", 
             font=("Arial", 16, "bold"), bg="lightgray").pack(pady=10)
    
    tk.Label(frame, text="Katalogų pasirinkimas:", bg="lightgray").pack(anchor="w", padx=10)
    
    text_frame = tk.Frame(frame, bg="lightgray")
    text_frame.pack(fill="both", expand=True, padx=10, pady=5)
    
    text_widget = tk.Text(text_frame, height=8, bg="white", relief="sunken", bd=2)
    scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    text_widget.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    button_frame = tk.Frame(frame, bg="lightgray")
    button_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Button(button_frame, text="Pridėti katalogą", bg="lightblue").pack(side="left", padx=5)
    tk.Button(button_frame, text="Apdoroti failus", bg="lightgreen").pack(side="left", padx=5)
    tk.Button(button_frame, text="Eksportuoti", bg="orange").pack(side="left", padx=5)
    
    # Progress bar
    progress = ttk.Progressbar(frame, mode='determinate')
    progress.pack(fill="x", padx=10, pady=5)
    
    root.mainloop()

def show_modern_style():
    """Show modern CustomTkinter style window"""
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    
    root = ctk.CTk()
    root.title("Modern CustomTkinter Style")
    root.geometry("600x400")
    
    # Modern style widgets
    main_frame = ctk.CTkFrame(root)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    title = ctk.CTkLabel(main_frame, text="Excel Duomenų Apdorojimas", 
                        font=ctk.CTkFont(size=20, weight="bold"))
    title.pack(pady=(20, 30))
    
    # Directory section
    dir_frame = ctk.CTkFrame(main_frame)
    dir_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    dir_label = ctk.CTkLabel(dir_frame, text="📁 Katalogų Pasirinkimas", 
                            font=ctk.CTkFont(size=16, weight="bold"))
    dir_label.pack(pady=(20, 10))
    
    text_widget = ctk.CTkTextbox(dir_frame, height=120)
    text_widget.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    # Buttons
    button_frame = ctk.CTkFrame(main_frame)
    button_frame.pack(fill="x", padx=20, pady=(0, 20))
    
    ctk.CTkButton(button_frame, text="➕ Pridėti Katalogą", height=40).pack(side="left", padx=(20, 10), pady=20)
    ctk.CTkButton(button_frame, text="🚀 Apdoroti Failus", height=40).pack(side="left", padx=(0, 10), pady=20)
    ctk.CTkButton(button_frame, text="📊 Eksportuoti", height=40).pack(side="left", padx=(0, 20), pady=20)
    
    # Modern progress bar
    progress = ctk.CTkProgressBar(main_frame, height=20)
    progress.pack(fill="x", padx=20, pady=(0, 20))
    progress.set(0.3)  # Show some progress
    
    root.mainloop()

if __name__ == "__main__":
    import sys
    
    print("Choose demo:")
    print("1. Old Tkinter Style")
    print("2. Modern CustomTkinter Style")
    print("3. Both (side by side)")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        show_old_style()
    elif choice == "2":
        show_modern_style()
    elif choice == "3":
        import threading
        
        # Run both in separate threads
        thread1 = threading.Thread(target=show_old_style, daemon=True)
        thread2 = threading.Thread(target=show_modern_style, daemon=True)
        
        thread1.start()
        thread2.start()
        
        # Keep main thread alive
        try:
            thread1.join()
            thread2.join()
        except KeyboardInterrupt:
            pass
    else:
        print("Invalid choice. Running modern style by default.")
        show_modern_style()

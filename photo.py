import sys
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QLabel, QLineEdit, QPushButton, QVBoxLayout, QFileDialog
from PyQt5.QtGui import QPixmap
from PIL import Image, ImageDraw, ImageFont
from psd_tools import PSDImage

class PDFGenerator(QWidget):
    def __init__(self):
        super().__init__()

        self.initUI()

    def initUI(self):
        self.name_label = QLabel('Name:')
        self.name_edit = QLineEdit()

        self.bounty_label = QLabel('Bounty:')
        self.bounty_edit = QLineEdit()

        self.reason_label = QLabel('Reason:')
        self.reason_edit = QLineEdit()

        self.image_label = QLabel('Drag and drop image here:')
        self.image_preview = QLabel()
        self.image_path = None

        self.submit_button = QPushButton('Submit')
        self.submit_button.clicked.connect(self.generate_pdf)

        layout = QVBoxLayout()
        layout.addWidget(self.name_label)
        layout.addWidget(self.name_edit)
        layout.addWidget(self.bounty_label)
        layout.addWidget(self.bounty_edit)
        layout.addWidget(self.reason_label)
        layout.addWidget(self.reason_edit)
        layout.addWidget(self.image_label)
        layout.addWidget(self.image_preview)
        layout.addWidget(self.submit_button)

        self.setLayout(layout)

        self.setWindowTitle('PDF Generator')
        self.setGeometry(300, 300, 400, 300)

    def dragEnterEvent(self, e):
        e.accept()

    def dropEvent(self, e):
        mime_data = e.mimeData()
        if mime_data.hasUrls():
            url = mime_data.urls()[0]
            self.image_path = url.toLocalFile()
            self.show_image_preview()

    def show_image_preview(self):
        pixmap = QPixmap(self.image_path)
        self.image_preview.setPixmap(pixmap)
        self.image_preview.setScaledContents(True)

    def generate_pdf(self):
        if self.image_path:
            psd_path = 'C:/Users/<USER>/Desktop/test.psd'
            output_path = 'C:/Users/<USER>/Desktop/output.pdf'

            # Open PSD file
            psd = PSDImage.open(psd_path)

            # Edit text layers
            psd.tree().visit(lambda layer: setattr(layer, 'text', self.get_text_for_layer(layer)))

            # Place image as a layer
            image_layer = psd.layers['Photo']
            image = Image.open(self.image_path)
            image_layer.compose(image)

            # Save modified PSD
            psd.save(output_path)

            print(f"PDF generated and saved to {output_path}")

    def get_text_for_layer(self, layer):
        if layer.name == 'Name':
            return f"Name: {self.name_edit.text()}"
        elif layer.name == 'Bounty':
            return f"Bounty: {self.bounty_edit.text()}"
        elif layer.name == 'Reason':
            return f"Reason: {self.reason_edit.text()}"
        else:
            return layer.text

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = PDFGenerator()
    ex.show()
    sys.exit(app.exec_())

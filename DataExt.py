import os
import json
import threading
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pandas as pd
from openpyxl import load_workbook, Workbook
from openpyxl.utils.exceptions import InvalidFileException
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from zipfile import BadZipFile
import logging
from datetime import datetime
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages application configuration"""
    def __init__(self, config_file: str = "excel_processor_config.json"):
        self.config_file = config_file
        self.default_config = {
            "themes": [
                {"label": "<PERSON>tika", "display_name": "<PERSON>tika", "left_cell": "N9", "right_cell": ""},
                {"label": "Tik<PERSON><PERSON>", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "left_cell": "N10", "right_cell": ""},
                {"label": "Lietuvių kalba", "display_name": "Lietuvių kalba ir literatūra", "left_cell": "N11", "right_cell": ""},
                {"label": "Matematika", "display_name": "Matematika", "left_cell": "N12", "right_cell": ""},
                {"label": "Fizinis", "display_name": "Fizinis ugdymas", "left_cell": "N13", "right_cell": ""},
                {"label": "Dailė", "display_name": "Dailė", "left_cell": "N14", "right_cell": ""},
                {"label": "Muzika", "display_name": "Muzika", "left_cell": "N15", "right_cell": ""},
                {"label": "Šokis", "display_name": "Šokis", "left_cell": "N16", "right_cell": ""},
                {"label": "Technologijos", "display_name": "Taikomosios technologijos", "left_cell": "N18", "right_cell": ""},
                {"label": "Anglų", "display_name": "Užsienio kalba (anglų) (1-oji) + modulis", "left_cell": "N21", "right_cell": ""},
                {"label": "Biologija", "display_name": "Biologija", "left_cell": "N22", "right_cell": ""},
                {"label": "Chemija", "display_name": "Chemija", "left_cell": "N23", "right_cell": ""},
                {"label": "Fizika", "display_name": "Fizika", "left_cell": "N24", "right_cell": ""},
                {"label": "Informatika", "display_name": "Informatika + modulis", "left_cell": "N25", "right_cell": ""},
                {"label": "Istorija", "display_name": "Istorija", "left_cell": "N26", "right_cell": ""},
                {"label": "Geografija", "display_name": "Geografija", "left_cell": "N27", "right_cell": ""},
                {"label": "Ekonomika", "display_name": "Ekonomika ir verslumas", "left_cell": "N28", "right_cell": ""},
                {"label": "Ispanų", "display_name": "Užsienio kalba (ispanų)", "left_cell": "H31", "right_cell": ""},
                {"label": "Vokiečių", "display_name": "Užsienio kalba (vokiečių)", "left_cell": "P31", "right_cell": ""},
                {"label": "Prancūzų", "display_name": "Užsienio kalba (prancūzų)", "left_cell": "G32", "right_cell": ""},
                {"label": "MBiologija", "display_name": "Biologija", "left_cell": "G35", "right_cell": ""},
                {"label": "MChemija", "display_name": "Chemija", "left_cell": "P35", "right_cell": ""},
                {"label": "MFizika", "display_name": "Fizika", "left_cell": "G36", "right_cell": ""},
                {"label": "MGeografija", "display_name": "Geografija", "left_cell": "P36", "right_cell": ""},
                {"label": "MIstorija", "display_name": "Istorija", "left_cell": "G37", "right_cell": ""},
                {"label": "MLietuvių", "display_name": "Lietuvių", "left_cell": "P37", "right_cell": ""},
            ],
            "last_directories": [],
            "name_cells": {"first_name": "D4", "last_name": "J4"},
            "class_cell": "",
            "max_workers": 4,
            "auto_save": True
        }

    def load(self) -> dict:
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # Merge with defaults to ensure all keys exist
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                # Ensure all themes have display_name
                for theme in config["themes"]:
                    if "display_name" not in theme:
                        theme["display_name"] = theme["label"]
                return config
        except (FileNotFoundError, json.JSONDecodeError):
            return self.default_config.copy()

    def save(self, config: dict):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save config: {e}")


class ExcelProcessor:
    """Handles Excel file processing logic"""
    def __init__(self, config: dict):
        self.config = config
        self.results_by_folder = defaultdict(dict)
        self.all_results = {}
        self.errors = []

    def process_file(self, file_path: str, folder_name: str) -> Tuple[Optional[str], Optional[dict], Optional[str]]:
        """Process a single Excel file"""
        try:
            wb = load_workbook(file_path, data_only=True, read_only=True)
            ws = wb.active

            # Extract student info
            first_name = str(ws[self.config["name_cells"]["first_name"]].value or "").strip().title()
            last_name = str(ws[self.config["name_cells"]["last_name"]].value or "").strip().title()
            full_name = f"{first_name} {last_name}".strip()

            # Get class if configured
            class_value = ""
            if self.config.get("class_cell"):
                class_value = str(ws[self.config["class_cell"]].value or "").strip()

            result = {
                "Vardas": first_name,
                "Pavardė": last_name,
                "Klasė": class_value
            }

            # Extract theme values using display names
            for theme in self.config["themes"]:
                theme_display_name = theme.get("display_name", theme["label"])
                left_cell = theme["left_cell"]
                right_cell = theme.get("right_cell", "")

                value = None
                if left_cell:
                    value = ws[left_cell].value
                if right_cell and value is None:
                    value = ws[right_cell].value

                result[theme_display_name] = value

            wb.close()
            return full_name, result, None

        except (BadZipFile, InvalidFileException) as e:
            return None, None, f"Invalid file format: {os.path.basename(file_path)}"
        except Exception as e:
            return None, None, f"Error processing {os.path.basename(file_path)}: {str(e)}"

    def process_directories(self, directories: List[str], progress_callback=None) -> Tuple[Dict[str, dict], Dict[str, dict], List[str]]:
        """Process all Excel files in multiple directories"""
        self.results_by_folder.clear()
        self.all_results.clear()
        self.errors.clear()

        total_files = 0
        files_by_directory = {}

        # Count total files and organize by directory
        for directory in directories:
            excel_files = []
            for ext in ['.xlsx', '.xlsm']:
                excel_files.extend(Path(directory).glob(f"*{ext}"))
            files_by_directory[directory] = excel_files
            total_files += len(excel_files)

        if total_files == 0:
            return {}, {}, ["No Excel files found in selected directories"]

        processed = 0

        # Process each directory
        for directory, files in files_by_directory.items():
            folder_name = Path(directory).name

            # Process files in parallel for this directory
            with ThreadPoolExecutor(max_workers=self.config.get("max_workers", 4)) as executor:
                future_to_file = {
                    executor.submit(self.process_file, str(file), folder_name): file
                    for file in files
                }

                for future in as_completed(future_to_file):
                    file = future_to_file[future]
                    full_name, result, error = future.result()

                    if error:
                        self.errors.append(f"[{folder_name}] {error}")
                    elif full_name and result:
                        # Add to folder-specific results
                        self.results_by_folder[folder_name][full_name] = result
                        # Add to all results
                        self.all_results[full_name] = result

                    processed += 1
                    if progress_callback:
                        progress_callback(processed, total_files)

        return self.results_by_folder, self.all_results, self.errors


class ExcelProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel duomenų apdorojimas - Enhanced Multi-Folder")
        self.root.geometry("1100x750")

        # Initialize components
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load()
        self.processor = ExcelProcessor(self.config)

        # Variables
        self.selected_directories = []
        self.processing = False

        # Create UI
        self.create_ui()
        self.load_themes_to_tree()
        self.update_directory_list()

        # Set window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_ui(self):
        """Create the user interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Main processing tab
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="Pagrindinis")
        self.create_main_tab(main_tab)

        # Configuration tab
        config_tab = ttk.Frame(notebook)
        notebook.add(config_tab, text="Konfigūracija")
        self.create_config_tab(config_tab)

        # Results preview tab
        preview_tab = ttk.Frame(notebook)
        notebook.add(preview_tab, text="Rezultatų peržiūra")
        self.create_preview_tab(preview_tab)

        # Status bar
        self.status_bar = ttk.Label(self.root, text="Pasiruošta", relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_main_tab(self, parent):
        """Create main processing tab"""
        # Directory selection
        dir_frame = ttk.LabelFrame(parent, text="Pasirinkite katalogus su Excel failais", padding=10)
        dir_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Directory list
        list_frame = ttk.Frame(dir_frame)
        list_frame.pack(fill="both", expand=True)

        # Create listbox with scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.dir_listbox = tk.Listbox(list_frame, height=8, yscrollcommand=scrollbar.set, selectmode=tk.EXTENDED)
        self.dir_listbox.pack(side=tk.LEFT, fill="both", expand=True)
        scrollbar.config(command=self.dir_listbox.yview)

        # Directory buttons
        btn_frame = ttk.Frame(dir_frame)
        btn_frame.pack(fill="x", pady=5)

        ttk.Button(btn_frame, text="Pridėti katalogą", command=self.add_directory).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="Pašalinti pasirinktus", command=self.remove_directories).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="Išvalyti sąrašą", command=self.clear_directories).pack(side=tk.LEFT, padx=2)

        # Action buttons
        action_frame = ttk.Frame(parent)
        action_frame.pack(pady=20)

        self.process_btn = ttk.Button(
            action_frame,
            text="Apdoroti Excel failus",
            command=self.process_files,
            style="Accent.TButton"
        )
        self.process_btn.pack(side=tk.LEFT, padx=5)

        self.export_btn = ttk.Button(
            action_frame,
            text="Eksportuoti į Excel",
            command=self.export_data
        )
        self.export_btn.pack(side=tk.LEFT, padx=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            parent,
            variable=self.progress_var,
            maximum=100,
            length=400
        )
        self.progress_bar.pack(pady=10)

        # Results summary
        summary_frame = ttk.LabelFrame(parent, text="Apdorojimo rezultatai", padding=5)
        summary_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.summary_text = tk.Text(summary_frame, height=8, width=70, wrap=tk.WORD)
        self.summary_text.pack(fill="both", expand=True)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.summary_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.summary_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.summary_text.yview)

    def create_config_tab(self, parent):
        """Create configuration tab"""
        # Theme configuration
        theme_frame = ttk.LabelFrame(parent, text="Temų koordinatės ir pavadinimai", padding=10)
        theme_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Create treeview
        columns = ("Pavadinimas", "Rodyti", "Kairysis", "Dešinysis")
        self.tree = ttk.Treeview(theme_frame, columns=columns, show="headings", height=15)

        # Configure columns
        self.tree.heading("Pavadinimas", text="Vidinis pavadinimas")
        self.tree.heading("Rodyti", text="Rodomas pavadinimas")
        self.tree.heading("Kairysis", text="Kairysis langelis")
        self.tree.heading("Dešinysis", text="Dešinysis langelis")

        self.tree.column("Pavadinimas", width=150)
        self.tree.column("Rodyti", width=200)
        self.tree.column("Kairysis", width=120)
        self.tree.column("Dešinysis", width=120)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(theme_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

        theme_frame.grid_rowconfigure(0, weight=1)
        theme_frame.grid_columnconfigure(0, weight=1)

        # Enable editing on double-click
        self.tree.bind("<Double-1>", self.on_double_click)

        # Button frame
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill="x", pady=5)

        ttk.Button(btn_frame, text="Pridėti temą", command=self.add_theme).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="Pašalinti", command=self.remove_theme).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="↑ Aukštyn", command=lambda: self.move_theme(-1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="↓ Žemyn", command=lambda: self.move_theme(1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="Išsaugoti konfigūraciją", command=self.save_config).pack(side=tk.LEFT, padx=20)

        # Additional settings
        settings_frame = ttk.LabelFrame(parent, text="Papildomi nustatymai", padding=10)
        settings_frame.pack(fill="x", padx=10, pady=5)

        # Name cells configuration
        ttk.Label(settings_frame, text="Vardo langelis:").grid(row=0, column=0, sticky="w", padx=5)
        self.name_cell_var = tk.StringVar(value=self.config["name_cells"]["first_name"])
        ttk.Entry(settings_frame, textvariable=self.name_cell_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(settings_frame, text="Pavardės langelis:").grid(row=0, column=2, sticky="w", padx=5)
        self.surname_cell_var = tk.StringVar(value=self.config["name_cells"]["last_name"])
        ttk.Entry(settings_frame, textvariable=self.surname_cell_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(settings_frame, text="Klasės langelis:").grid(row=1, column=0, sticky="w", padx=5)
        self.class_cell_var = tk.StringVar(value=self.config.get("class_cell", ""))
        ttk.Entry(settings_frame, textvariable=self.class_cell_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(settings_frame, text="Lygiagrečių procesų:").grid(row=1, column=2, sticky="w", padx=5)
        self.workers_var = tk.IntVar(value=self.config.get("max_workers", 4))
        ttk.Spinbox(settings_frame, from_=1, to=8, textvariable=self.workers_var, width=10).grid(row=1, column=3, padx=5)

    def create_preview_tab(self, parent):
        """Create results preview tab"""
        # Folder selection
        folder_frame = ttk.Frame(parent)
        folder_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(folder_frame, text="Peržiūrėti katalogą:").pack(side=tk.LEFT, padx=5)
        self.preview_folder_var = tk.StringVar()
        self.preview_folder_combo = ttk.Combobox(folder_frame, textvariable=self.preview_folder_var, width=40)
        self.preview_folder_combo.pack(side=tk.LEFT, padx=5)
        self.preview_folder_combo.bind("<<ComboboxSelected>>", self.on_preview_folder_changed)

        # Create frame for treeview
        preview_frame = ttk.Frame(parent)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create treeview for results
        self.results_tree = ttk.Treeview(preview_frame, show="headings", height=20)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.results_tree.yview)
        x_scrollbar = ttk.Scrollbar(preview_frame, orient="horizontal", command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=y_scrollbar.set, xscrollcommand=x_scrollbar.set)

        # Grid layout
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        y_scrollbar.grid(row=0, column=1, sticky="ns")
        x_scrollbar.grid(row=1, column=0, sticky="ew")

        preview_frame.grid_rowconfigure(0, weight=1)
        preview_frame.grid_columnconfigure(0, weight=1)

        # Export selected button
        ttk.Button(parent, text="Eksportuoti pasirinktus", command=self.export_selected).pack(pady=5)

    def on_double_click(self, event):
        """Handle double-click on treeview item for editing"""
        item = self.tree.selection()[0]
        column = self.tree.identify_column(event.x)

        if column:
            col_index = int(column.replace('#', '')) - 1

            # Get current value
            values = list(self.tree.item(item, 'values'))
            current_value = values[col_index]

            # Create entry widget for editing
            x, y, width, height = self.tree.bbox(item, column)

            entry = ttk.Entry(self.tree)
            entry.place(x=x, y=y, width=width, height=height)
            entry.insert(0, current_value)
            entry.focus()
            entry.select_range(0, tk.END)

            def save_edit(event=None):
                values[col_index] = entry.get()
                self.tree.item(item, values=values)
                entry.destroy()
                if self.config.get("auto_save", True):
                    self.save_config()

            def cancel_edit(event=None):
                entry.destroy()

            entry.bind('<Return>', save_edit)
            entry.bind('<Escape>', cancel_edit)
            entry.bind('<FocusOut>', save_edit)

    def load_themes_to_tree(self):
        """Load themes from config to treeview"""
        for theme in self.config["themes"]:
            self.tree.insert("", "end", values=(
                theme["label"],
                theme.get("display_name", theme["label"]),
                theme["left_cell"],
                theme.get("right_cell", "")
            ))

    def save_config(self):
        """Save current configuration"""
        # Update themes from tree
        self.config["themes"] = []
        for item in self.tree.get_children():
            label, display_name, left, right = self.tree.item(item)["values"]
            if label and left:
                self.config["themes"].append({
                    "label": label,
                    "display_name": display_name or label,
                    "left_cell": left,
                    "right_cell": right or ""
                })

        # Update other settings
        self.config["name_cells"]["first_name"] = self.name_cell_var.get()
        self.config["name_cells"]["last_name"] = self.surname_cell_var.get()
        self.config["class_cell"] = self.class_cell_var.get()
        self.config["max_workers"] = self.workers_var.get()
        self.config["last_directories"] = self.selected_directories

        # Save to file
        self.config_manager.save(self.config)
        self.processor.config = self.config
        self.update_status("Konfigūracija išsaugota")

    def add_directory(self):
        """Add directory to the list"""
        directory = filedialog.askdirectory()
        if directory and directory not in self.selected_directories:
            self.selected_directories.append(directory)
            self.update_directory_list()

    def remove_directories(self):
        """Remove selected directories from the list"""
        selected_indices = self.dir_listbox.curselection()
        for index in reversed(selected_indices):
            del self.selected_directories[index]
        self.update_directory_list()

    def clear_directories(self):
        """Clear all directories"""
        self.selected_directories.clear()
        self.update_directory_list()

    def update_directory_list(self):
        """Update the directory listbox"""
        self.dir_listbox.delete(0, tk.END)
        for directory in self.selected_directories:
            self.dir_listbox.insert(tk.END, directory)

    def process_files(self):
        """Process Excel files in selected directories"""
        if not self.selected_directories:
            messagebox.showerror("Klaida", "Pirmiausia pasirinkite bent vieną katalogą.")
            return

        if self.processing:
            messagebox.showinfo("Informacija", "Apdorojimas jau vyksta.")
            return

        # Save config before processing
        self.save_config()

        # Clear previous results
        self.summary_text.delete(1.0, tk.END)
        self.progress_var.set(0)

        # Start processing in thread
        self.processing = True
        self.process_btn.config(state="disabled")
        self.update_status("Apdorojami failai...")

        thread = threading.Thread(target=self._process_files_thread)
        thread.start()

    def _process_files_thread(self):
        """Process files in separate thread"""
        def update_progress(current, total):
            progress = (current / total) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.update_status(f"Apdorota {current}/{total} failų"))

        # Process files
        results_by_folder, all_results, errors = self.processor.process_directories(
            self.selected_directories, update_progress
        )

        # Update UI in main thread
        self.root.after(0, lambda: self._process_complete(results_by_folder, all_results, errors))

    def _process_complete(self, results_by_folder, all_results, errors):
        """Handle processing completion"""
        self.processing = False
        self.process_btn.config(state="normal")

        # Update summary
        self.summary_text.delete(1.0, tk.END)
        summary = f"Apdorojimo rezultatai:\n"
        summary += f"{'='*50}\n"
        summary += f"Iš viso apdorota failų: {len(all_results)}\n"
        summary += f"Apdoroti katalogai: {len(results_by_folder)}\n\n"

        # Summary by folder
        for folder_name, results in results_by_folder.items():
            summary += f"• {folder_name}: {len(results)} failų\n"

        if errors:
            summary += f"\nFailų su klaidomis: {len(errors)}\n"
            for error in errors[:10]:  # Show first 10 errors
                summary += f"  - {error}\n"
            if len(errors) > 10:
                summary += f"  ... ir dar {len(errors) - 10} klaidų\n"

        self.summary_text.insert(1.0, summary)

        # Update preview combo
        folder_names = ["Visi"] + list(results_by_folder.keys())
        self.preview_folder_combo['values'] = folder_names
        if folder_names:
            self.preview_folder_combo.set(folder_names[0])
            self.update_results_preview("Visi")

        self.update_status(f"Apdorojimas baigtas. Apdorota {len(all_results)} failų.")

        if all_results:
            messagebox.showinfo("Baigta", f"Sėkmingai apdorota {len(all_results)} failų iš {len(results_by_folder)} katalogų.")

    def on_preview_folder_changed(self, event=None):
        """Handle preview folder selection change"""
        selected_folder = self.preview_folder_var.get()
        self.update_results_preview(selected_folder)

    def update_results_preview(self, folder_name):
        """Update results preview treeview"""
        # Clear existing items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Get appropriate results
        if folder_name == "Visi":
            results = self.processor.all_results
        else:
            results = self.processor.results_by_folder.get(folder_name, {})

        if not results:
            return

        # Convert to DataFrame for sorting, keeping original index
        df = pd.DataFrame.from_dict(results, orient='index')
        df['original_key'] = df.index  # Store original keys

        # Apply Lithuanian sorting
        if folder_name == "Visi":
            df = self.sort_dataframe_lithuanian(df, by_class=True)
        else:
            df = self.sort_dataframe_lithuanian(df, by_class=False)

        # Get columns (excluding our helper column)
        columns = [col for col in df.columns if col != 'original_key']

        # Configure columns
        self.results_tree["columns"] = columns
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120)

        # Add data
        for idx, row in df.iterrows():
            values = [row[col] for col in columns]
            original_key = row['original_key']
            self.results_tree.insert("", "end", values=values, tags=(original_key,))

    def lithuanian_sort_key(self, text):
        """Create sort key for Lithuanian alphabetical order"""
        # Lithuanian alphabet order
        lithuanian_order = {
            'a': '01', 'ą': '02', 'b': '03', 'c': '04', 'č': '05', 'd': '06', 'e': '07', 'ę': '08', 'ė': '09',
            'f': '10', 'g': '11', 'h': '12', 'i': '13', 'į': '14', 'y': '15', 'j': '16', 'k': '17',
            'l': '18', 'ł': '19', 'm': '20', 'n': '21', 'o': '22', 'p': '23', 'r': '24', 's': '25',
            'š': '26', 't': '27', 'u': '28', 'ų': '29', 'ū': '30', 'v': '31', 'z': '32', 'ž': '33'
        }

        result = []
        for char in str(text).lower():
            if char in lithuanian_order:
                result.append(lithuanian_order[char])
            else:
                # For non-Lithuanian characters, use Unicode value
                result.append(f"{ord(char):04d}")

        return ''.join(result)

    def sort_dataframe_lithuanian(self, df, by_class=False):
        """Sort DataFrame using Lithuanian alphabetical order"""
        # Create a copy to avoid modifying the original
        df = df.copy()

        if by_class:
            # Create sort keys for class, surname, and name
            df['_sort_class'] = df['Klasė'].fillna('').apply(self.lithuanian_sort_key)
            df['_sort_surname'] = df['Pavardė'].fillna('').apply(self.lithuanian_sort_key)
            df['_sort_name'] = df['Vardas'].fillna('').apply(self.lithuanian_sort_key)

            # Sort by all three keys
            df = df.sort_values(by=['_sort_class', '_sort_surname', '_sort_name'])

            # Remove temporary columns
            df = df.drop(columns=['_sort_class', '_sort_surname', '_sort_name'])
        else:
            # Create sort keys for surname and name
            df['_sort_surname'] = df['Pavardė'].fillna('').apply(self.lithuanian_sort_key)
            df['_sort_name'] = df['Vardas'].fillna('').apply(self.lithuanian_sort_key)

            # Sort by surname and name
            df = df.sort_values(by=['_sort_surname', '_sort_name'])

            # Remove temporary columns
            df = df.drop(columns=['_sort_surname', '_sort_name'])

        return df

    def create_exact_format_export(self, worksheet, df):
        """Apply the exact format from the example file with dynamic headers and bottom counts"""
        # Build headers dynamically from configuration
        base_headers = ["Nr.", "Vardas", "Pavardė", "Klasė"]

        # Get theme headers from configuration in order
        theme_headers = []
        for theme in self.config["themes"]:
            theme_headers.append(theme.get("display_name", theme["label"]))

        # Complete headers list
        headers = base_headers + theme_headers + ["Suma", "Parašas"]

        # Write headers
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx)
            cell.value = header

        # Set dynamic column widths
        exact_widths = {
            'A': 6,          # Nr.
            'B': 10.7,       # Vardas
            'C': 16.1,       # Pavardė
            'D': 8,          # Klasė
        }

        # Subject columns - narrow width for all theme columns
        theme_start_col = 5  # Column E
        for i in range(len(theme_headers)):
            col_letter = get_column_letter(theme_start_col + i)
            exact_widths[col_letter] = 4.3

        # Special columns (Suma and Parašas)
        suma_col = get_column_letter(theme_start_col + len(theme_headers))
        paras_col = get_column_letter(theme_start_col + len(theme_headers) + 1)
        exact_widths[suma_col] = 5.9  # Suma
        exact_widths[paras_col] = 8   # Parašas

        # Apply column widths
        for col, width in exact_widths.items():
            worksheet.column_dimensions[col].width = width

        # Set header row height (convert from points to Excel units)
        worksheet.row_dimensions[1].height = 180 * 0.75  # 180 points = 135 Excel units

        # Write data
        data_end_row = 1  # Track where data ends
        for row_idx, (_, row_data) in enumerate(df.iterrows(), 2):
            data_end_row = row_idx

            # Nr.
            worksheet.cell(row=row_idx, column=1).value = row_idx - 1

            # Basic info
            worksheet.cell(row=row_idx, column=2).value = row_data.get('Vardas', '')
            worksheet.cell(row=row_idx, column=3).value = row_data.get('Pavardė', '')
            worksheet.cell(row=row_idx, column=4).value = row_data.get('Klasė', '')

            # Theme subjects - write values from the processed data
            col_idx = 5
            suma_range_start = get_column_letter(5)
            suma_range_end = get_column_letter(4 + len(theme_headers))

            for theme_header in theme_headers:
                value = None
                if theme_header in row_data.index:
                    value = row_data[theme_header]
                    if pd.notna(value) and value != '' and value != 0:
                        worksheet.cell(row=row_idx, column=col_idx).value = value
                col_idx += 1

            # Add SUM formula in "Suma" column
            suma_col_idx = 4 + len(theme_headers) + 1
            suma_cell = worksheet.cell(row=row_idx, column=suma_col_idx)
            suma_cell.value = f"=SUM({suma_range_start}{row_idx}:{suma_range_end}{row_idx})"

            # Leave "Parašas" column empty

        # Add count totals at the bottom (like in the example)
        self.add_bottom_counts(worksheet, headers, data_end_row + 2)

        # Apply styling
        self.apply_exact_styling(worksheet, data_end_row + 3)

    def add_bottom_counts(self, worksheet, headers, count_row):
        """Add count totals at the bottom of the worksheet like in the example"""
        # Skip the first 4 columns (Nr., Vardas, Pavardė, Klasė) and last 2 (Suma, Parašas)
        theme_start_col = 5
        theme_end_col = len(headers) - 2  # Exclude Suma and Parašas

        # Add the header names in the count row (rotated like in example)
        for col_idx in range(theme_start_col, theme_end_col + 1):
            header_cell = worksheet.cell(row=count_row, column=col_idx)
            header_cell.value = headers[col_idx - 1]  # -1 because headers is 0-indexed
            header_cell.alignment = Alignment(text_rotation=90, horizontal="center", vertical="bottom")
            header_cell.font = Font(size=9)

        # Add the count formulas in the row below
        count_formula_row = count_row + 1
        for col_idx in range(theme_start_col, theme_end_col + 1):
            col_letter = get_column_letter(col_idx)
            count_cell = worksheet.cell(row=count_formula_row, column=col_idx)
            # Count non-empty cells in this column (excluding header and count rows)
            count_cell.value = f"=COUNTA({col_letter}2:{col_letter}{count_row - 1})"
            count_cell.alignment = Alignment(horizontal="center", vertical="center")
            count_cell.font = Font(size=10, bold=True)

    def apply_exact_styling(self, worksheet, max_data_row=None):
        """Apply exact styling to match the example file"""
        # Define styles
        header_font = Font(bold=True, size=11)
        normal_font = Font(size=11)

        # Define border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Determine the actual max column and row
        max_col = worksheet.max_column
        if max_data_row is None:
            max_row = worksheet.max_row
        else:
            max_row = max_data_row

        # Style headers
        for col in range(1, max_col + 1):
            header_cell = worksheet.cell(row=1, column=col)
            header_cell.font = header_font
            header_cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            header_cell.border = thin_border

        # Style data rows (excluding count rows at bottom)
        data_end_row = max_data_row - 2 if max_data_row else max_row
        for row in range(2, data_end_row + 1):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.font = normal_font
                cell.border = thin_border

                # Set alignment based on column
                if col == 1:  # Nr. column - center
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                elif col in [2, 3, 4]:  # Name columns - left
                    cell.alignment = Alignment(horizontal="left", vertical="center")
                elif col == max_col:  # Parašas column (last column) - center
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:  # Subject columns and Suma - center
                    cell.alignment = Alignment(horizontal="center", vertical="center")

        # Style count rows at bottom if they exist
        if max_data_row:
            count_start_row = max_data_row - 1
            for row in range(count_start_row, max_data_row + 1):
                for col in range(1, max_col + 1):
                    cell = worksheet.cell(row=row, column=col)
                    if cell.value:  # Only style cells with content
                        cell.border = thin_border

        # Freeze panes at A2 (freeze header row)
        worksheet.freeze_panes = 'A2'

    def export_data(self):
        """Export processed data to Excel with exact format matching"""
        if not self.processor.all_results:
            messagebox.showinfo("Informacija", "Nėra duomenų eksportavimui.")
            return

        # Ask for file location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel failai", "*.xlsx")],
            initialfile=f"planai_{datetime.now().strftime('%Y_%m_%d_%H%M%S')}.xlsx"
        )

        if not file_path:
            return

        try:
            # Create workbook
            wb = Workbook()

            # First sheet - "Bendras" (all students combined, sorted by class then surname)
            ws_bendras = wb.active
            ws_bendras.title = "Bendras"

            all_df = pd.DataFrame.from_dict(self.processor.all_results, orient='index')
            all_df = self.sort_dataframe_lithuanian(all_df, by_class=True)

            self.create_exact_format_export(ws_bendras, all_df)

            # Create separate sheets for each folder (class)
            for folder_name, results in sorted(self.processor.results_by_folder.items()):
                if results:
                    # Create new worksheet
                    ws = wb.create_sheet(title=folder_name[:31])  # Excel sheet name limit

                    df = pd.DataFrame.from_dict(results, orient='index')
                    df = self.sort_dataframe_lithuanian(df, by_class=False)

                    self.create_exact_format_export(ws, df)

            # Save workbook
            wb.save(file_path)

            messagebox.showinfo("Sėkmė", f"Duomenys sėkmingai eksportuoti į:\n{file_path}\n\nFailas turi tikslų formatą kaip pavyzdyje su:\n• Dinaminiais antraštės pavadinimais\n• Formulėmis ir dizainu\n• Skaičiavimais apačioje kiekvienai kolonai")
            self.update_status("Duomenys eksportuoti su tiksliu formatu ir skaičiavimais")

        except Exception as e:
            messagebox.showerror("Klaida", f"Nepavyko eksportuoti: {str(e)}")
            logger.error(f"Export error: {e}")

    def export_selected(self):
        """Export only selected results with exact format"""
        selected = self.results_tree.selection()
        if not selected:
            messagebox.showinfo("Informacija", "Pasirinkite įrašus eksportavimui.")
            return

        # Get current folder
        current_folder = self.preview_folder_var.get()

        # Get selected data
        selected_results = {}
        for item in selected:
            tag = self.results_tree.item(item)["tags"][0]
            if current_folder == "Visi":
                if tag in self.processor.all_results:
                    selected_results[tag] = self.processor.all_results[tag]
            else:
                folder_results = self.processor.results_by_folder.get(current_folder, {})
                if tag in folder_results:
                    selected_results[tag] = folder_results[tag]

        if not selected_results:
            return

        # Ask for file location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel failai", "*.xlsx")],
            initialfile=f"pasirinkti_planai_{datetime.now().strftime('%Y_%m_%d_%H%M%S')}.xlsx"
        )

        if not file_path:
            return

        try:
            df = pd.DataFrame.from_dict(selected_results, orient='index')
            # Use Lithuanian sorting - by class if from "Visi", otherwise just by name
            if current_folder == "Visi":
                df = self.sort_dataframe_lithuanian(df, by_class=True)
            else:
                df = self.sort_dataframe_lithuanian(df, by_class=False)

            # Create workbook with exact format
            wb = Workbook()
            ws = wb.active
            ws.title = "Pasirinkti mokiniai"

            self.create_exact_format_export(ws, df)

            wb.save(file_path)

            messagebox.showinfo("Sėkmė", f"Pasirinkti duomenys eksportuoti į:\n{file_path}\n\nSu tiksliu formatu:\n• Dinaminiais antraštės pavadinimais\n• Formulėmis ir dizainu\n• Skaičiavimais apačioje kiekvienai kolonai")

        except Exception as e:
            messagebox.showerror("Klaida", f"Nepavyko eksportuoti: {str(e)}")

    def add_theme(self):
        """Add new theme"""
        self.tree.insert("", "end", values=("Nauja", "Nauja tema", "", ""))

    def remove_theme(self):
        """Remove selected theme"""
        selected = self.tree.selection()
        for item in selected:
            self.tree.delete(item)

        if self.config.get("auto_save", True):
            self.save_config()

    def move_theme(self, direction):
        """Move selected theme up or down"""
        selected = self.tree.selection()
        if not selected:
            return

        item = selected[0]
        index = self.tree.index(item)
        parent = self.tree.parent(item)

        if direction == -1 and index > 0:  # Move up
            self.tree.move(item, parent, index - 1)
        elif direction == 1 and index < len(self.tree.get_children()) - 1:  # Move down
            self.tree.move(item, parent, index + 1)

        if self.config.get("auto_save", True):
            self.save_config()

    def update_status(self, message):
        """Update status bar"""
        self.status_bar.config(text=message)

    def on_closing(self):
        """Handle window closing"""
        if self.processing:
            if messagebox.askokcancel("Uždaryti", "Apdorojimas dar vyksta. Ar tikrai norite uždaryti?"):
                self.root.destroy()
        else:
            self.save_config()
            self.root.destroy()


def main():
    """Main application entry point"""
    root = tk.Tk()

    # Apply modern style
    style = ttk.Style()
    style.theme_use('clam')

    # Configure colors
    style.configure('Accent.TButton', foreground='white', background='#0078D4')
    style.map('Accent.TButton',
              background=[('active', '#106EBE'), ('pressed', '#005A9E')])

    # Create and run application
    app = ExcelProcessorApp(root)

    # Load last directories if any
    if app.config.get("last_directories"):
        app.selected_directories = app.config["last_directories"]
        app.update_directory_list()

    root.mainloop()


if __name__ == "__main__":
    main()
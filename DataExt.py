import os
import json
import threading
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pandas as pd
from openpyxl import load_workbook, Workbook
from openpyxl.utils.exceptions import InvalidFileException
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from zipfile import BadZipFile
import logging
from datetime import datetime
from collections import defaultdict
import time
import queue

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages application configuration"""
    def __init__(self, config_file: str = "excel_processor_config.json"):
        self.config_file = config_file
        self.default_config = {
            "themes": [
                {"label": "Etika", "display_name": "<PERSON><PERSON><PERSON>", "left_cell": "N9", "right_cell": ""},
                {"label": "Tikyba", "display_name": "Tikyba", "left_cell": "N10", "right_cell": ""},
                {"label": "Lietuvių kalba", "display_name": "Lietuvių kalba ir literatūra", "left_cell": "N11", "right_cell": ""},
                {"label": "Matematika", "display_name": "Matematika", "left_cell": "N12", "right_cell": ""},
                {"label": "Fizinis", "display_name": "Fizinis ugdymas", "left_cell": "N13", "right_cell": ""},
                {"label": "Dailė", "display_name": "Dailė", "left_cell": "N14", "right_cell": ""},
                {"label": "Muzika", "display_name": "Muzika", "left_cell": "N15", "right_cell": ""},
                {"label": "Šokis", "display_name": "Šokis", "left_cell": "N16", "right_cell": ""},
                {"label": "Technologijos", "display_name": "Taikomosios technologijos", "left_cell": "N18", "right_cell": ""},
                {"label": "Anglų", "display_name": "Užsienio kalba (anglų) (1-oji) + modulis", "left_cell": "N21", "right_cell": ""},
                {"label": "Biologija", "display_name": "Biologija", "left_cell": "N22", "right_cell": ""},
                {"label": "Chemija", "display_name": "Chemija", "left_cell": "N23", "right_cell": ""},
                {"label": "Fizika", "display_name": "Fizika", "left_cell": "N24", "right_cell": ""},
                {"label": "Informatika", "display_name": "Informatika + modulis", "left_cell": "N25", "right_cell": ""},
                {"label": "Istorija", "display_name": "Istorija", "left_cell": "N26", "right_cell": ""},
                {"label": "Geografija", "display_name": "Geografija", "left_cell": "N27", "right_cell": ""},
                {"label": "Ekonomika", "display_name": "Ekonomika ir verslumas", "left_cell": "N28", "right_cell": ""},
                {"label": "Ispanų", "display_name": "Užsienio kalba (ispanų)", "left_cell": "H31", "right_cell": ""},
                {"label": "Vokiečių", "display_name": "Užsienio kalba (vokiečių)", "left_cell": "P31", "right_cell": ""},
                {"label": "Prancūzų", "display_name": "Užsienio kalba (prancūzų)", "left_cell": "G32", "right_cell": ""},
                {"label": "MBiologija", "display_name": "Biologija", "left_cell": "G35", "right_cell": ""},
                {"label": "MChemija", "display_name": "Chemija", "left_cell": "P35", "right_cell": ""},
                {"label": "MFizika", "display_name": "Fizika", "left_cell": "G36", "right_cell": ""},
                {"label": "MGeografija", "display_name": "Geografija", "left_cell": "P36", "right_cell": ""},
                {"label": "MIstorija", "display_name": "Istorija", "left_cell": "G37", "right_cell": ""},
                {"label": "MLietuvių", "display_name": "Lietuvių", "left_cell": "P37", "right_cell": ""},
            ],
            "last_directories": [],
            "name_cells": {"first_name": "D4", "last_name": "J4"},
            "class_cell": "",
            "max_workers": 4,
            "auto_save": True
        }

    def load(self) -> dict:
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # Merge with defaults to ensure all keys exist
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                # Ensure all themes have display_name
                for theme in config["themes"]:
                    if "display_name" not in theme:
                        theme["display_name"] = theme["label"]
                return config
        except (FileNotFoundError, json.JSONDecodeError):
            return self.default_config.copy()

    def save(self, config: dict):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save config: {e}")


class ExcelProcessor:
    """Handles Excel file processing logic with performance optimizations"""
    def __init__(self, config: dict):
        self.config = config
        self.results_by_folder = defaultdict(dict)
        self.all_results = {}
        self.errors = []
        self._cache = {}  # Cache for processed files

    def process_file(self, file_path: str, folder_name: str) -> Tuple[Optional[str], Optional[dict], Optional[str]]:
        """Process a single Excel file with caching and optimizations"""
        # Check cache first
        file_stat = os.path.getmtime(file_path)
        cache_key = f"{file_path}_{file_stat}"

        if cache_key in self._cache:
            return self._cache[cache_key]

        try:
            # Use read_only and data_only for better performance
            wb = load_workbook(file_path, data_only=True, read_only=True)
            ws = wb.active

            # Pre-fetch all needed cells in one go for better performance
            name_cells = self.config["name_cells"]
            cells_to_read = [name_cells["first_name"], name_cells["last_name"]]

            if self.config.get("class_cell"):
                cells_to_read.append(self.config["class_cell"])

            # Add theme cells
            for theme in self.config["themes"]:
                if theme["left_cell"]:
                    cells_to_read.append(theme["left_cell"])
                if theme.get("right_cell"):
                    cells_to_read.append(theme["right_cell"])

            # Read all cells at once
            cell_values = {}
            for cell_ref in cells_to_read:
                try:
                    cell_values[cell_ref] = ws[cell_ref].value
                except:
                    cell_values[cell_ref] = None

            # Extract student info
            first_name = str(cell_values.get(name_cells["first_name"]) or "").strip().title()
            last_name = str(cell_values.get(name_cells["last_name"]) or "").strip().title()
            full_name = f"{first_name} {last_name}".strip()

            # Get class if configured
            class_value = ""
            if self.config.get("class_cell"):
                class_value = str(cell_values.get(self.config["class_cell"]) or "").strip()

            result = {
                "Vardas": first_name,
                "Pavardė": last_name,
                "Klasė": class_value
            }

            # Extract theme values using display names
            for theme in self.config["themes"]:
                theme_display_name = theme.get("display_name", theme["label"])
                left_cell = theme["left_cell"]
                right_cell = theme.get("right_cell", "")

                value = None
                if left_cell:
                    value = cell_values.get(left_cell)
                if right_cell and value is None:
                    value = cell_values.get(right_cell)

                result[theme_display_name] = value

            wb.close()

            # Cache the result
            result_tuple = (full_name, result, None)
            self._cache[cache_key] = result_tuple
            return result_tuple

        except (BadZipFile, InvalidFileException):
            error_msg = f"Invalid file format: {os.path.basename(file_path)}"
            result_tuple = (None, None, error_msg)
            self._cache[cache_key] = result_tuple
            return result_tuple
        except Exception as e:
            error_msg = f"Error processing {os.path.basename(file_path)}: {str(e)}"
            result_tuple = (None, None, error_msg)
            self._cache[cache_key] = result_tuple
            return result_tuple

    def process_directories(self, directories: List[str], progress_callback=None) -> Tuple[Dict[str, dict], Dict[str, dict], List[str]]:
        """Process all Excel files in multiple directories"""
        self.results_by_folder.clear()
        self.all_results.clear()
        self.errors.clear()

        total_files = 0
        files_by_directory = {}

        # Count total files and organize by directory
        for directory in directories:
            excel_files = []
            for ext in ['.xlsx', '.xlsm']:
                excel_files.extend(Path(directory).glob(f"*{ext}"))
            files_by_directory[directory] = excel_files
            total_files += len(excel_files)

        if total_files == 0:
            return {}, {}, ["No Excel files found in selected directories"]

        processed = 0

        # Process each directory
        for directory, files in files_by_directory.items():
            folder_name = Path(directory).name

            # Process files in parallel for this directory
            with ThreadPoolExecutor(max_workers=self.config.get("max_workers", 4)) as executor:
                future_to_file = {
                    executor.submit(self.process_file, str(file), folder_name): file
                    for file in files
                }

                for future in as_completed(future_to_file):
                    file = future_to_file[future]
                    full_name, result, error = future.result()

                    if error:
                        self.errors.append(f"[{folder_name}] {error}")
                    elif full_name and result:
                        # Add to folder-specific results
                        self.results_by_folder[folder_name][full_name] = result
                        # Add to all results
                        self.all_results[full_name] = result

                    processed += 1
                    if progress_callback:
                        progress_callback(processed, total_files)

        return self.results_by_folder, self.all_results, self.errors


class ExcelProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel duomenų apdorojimas - Modern Edition")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Configure modern styling
        self.setup_modern_theme()

        # Initialize components
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load()
        self.processor = ExcelProcessor(self.config)

        # Variables
        self.selected_directories = []
        self.processing = False
        self.progress_queue = queue.Queue()

        # Create UI
        self.create_ui()
        self.load_themes_to_tree()
        self.update_directory_list()

        # Set window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Start progress monitoring
        self.check_progress_queue()

    def setup_modern_theme(self):
        """Configure modern theme and styling"""
        style = ttk.Style()

        # Use a modern theme
        available_themes = style.theme_names()
        if 'vista' in available_themes:
            style.theme_use('vista')
        elif 'clam' in available_themes:
            style.theme_use('clam')
        else:
            style.theme_use('default')

        # Modern color scheme
        colors = {
            'primary': '#2563eb',      # Blue
            'primary_hover': '#1d4ed8',
            'primary_pressed': '#1e40af',
            'success': '#059669',      # Green
            'warning': '#d97706',      # Orange
            'danger': '#dc2626',       # Red
            'background': '#f8fafc',   # Light gray
            'surface': '#ffffff',      # White
            'text': '#1f2937',         # Dark gray
            'text_secondary': '#6b7280' # Medium gray
        }

        # Configure button styles
        style.configure('Primary.TButton',
                       foreground='white',
                       background=colors['primary'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))
        style.map('Primary.TButton',
                 background=[('active', colors['primary_hover']),
                           ('pressed', colors['primary_pressed'])])

        style.configure('Success.TButton',
                       foreground='white',
                       background=colors['success'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))
        style.map('Success.TButton',
                 background=[('active', '#047857'),
                           ('pressed', '#065f46')])

        style.configure('Warning.TButton',
                       foreground='white',
                       background=colors['warning'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))

        # Configure frame styles
        style.configure('Card.TFrame',
                       background=colors['surface'],
                       relief='flat',
                       borderwidth=1)

        # Configure label styles
        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=colors['text'])

        style.configure('Subheading.TLabel',
                       font=('Segoe UI', 10, 'bold'),
                       foreground=colors['text_secondary'])

        # Configure treeview
        style.configure('Modern.Treeview',
                       background=colors['surface'],
                       foreground=colors['text'],
                       fieldbackground=colors['surface'],
                       borderwidth=0,
                       font=('Segoe UI', 9))
        style.configure('Modern.Treeview.Heading',
                       background=colors['background'],
                       foreground=colors['text'],
                       font=('Segoe UI', 9, 'bold'))

        # Configure notebook
        style.configure('Modern.TNotebook',
                       background=colors['background'],
                       borderwidth=0)
        style.configure('Modern.TNotebook.Tab',
                       background=colors['background'],
                       foreground=colors['text'],
                       padding=(20, 10),
                       font=('Segoe UI', 10))
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', colors['surface']),
                           ('active', colors['primary'])])

    def check_progress_queue(self):
        """Check for progress updates from background threads"""
        try:
            while True:
                progress_data = self.progress_queue.get_nowait()
                if progress_data:
                    current, total = progress_data
                    percentage = (current / total) * 100 if total > 0 else 0
                    self.progress_var.set(percentage)
                    self.update_status(f"Apdorojama: {current}/{total} failų ({percentage:.1f}%)")
                    self.root.update_idletasks()
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.check_progress_queue)

    def create_ui(self):
        """Create the modern user interface"""
        # Main container with padding
        main_container = ttk.Frame(self.root, style='Card.TFrame')
        main_container.pack(fill="both", expand=True, padx=15, pady=15)

        # Create notebook for tabs with modern styling
        self.notebook = ttk.Notebook(main_container, style='Modern.TNotebook')
        self.notebook.pack(fill="both", expand=True)

        # Main processing tab
        main_tab = ttk.Frame(self.notebook, style='Card.TFrame')
        self.notebook.add(main_tab, text="📁 Pagrindinis")
        self.create_main_tab(main_tab)

        # Configuration tab
        config_tab = ttk.Frame(self.notebook, style='Card.TFrame')
        self.notebook.add(config_tab, text="⚙️ Konfigūracija")
        self.create_config_tab(config_tab)

        # Results preview tab
        preview_tab = ttk.Frame(self.notebook, style='Card.TFrame')
        self.notebook.add(preview_tab, text="📊 Rezultatų peržiūra")
        self.create_preview_tab(preview_tab)

        # Modern status bar
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=(0, 15))

        self.status_bar = ttk.Label(status_frame, text="✅ Pasiruošta",
                                   font=('Segoe UI', 9),
                                   foreground='#059669')
        self.status_bar.pack(side=tk.LEFT, padx=10, pady=5)

    def create_main_tab(self, parent):
        """Create modern main processing tab"""
        # Main content with padding
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_label = ttk.Label(content_frame, text="Excel failų apdorojimas",
                                style='Heading.TLabel')
        header_label.pack(anchor='w', pady=(0, 20))

        # Directory selection card
        dir_card = ttk.LabelFrame(content_frame, text="📁 Katalogų pasirinkimas",
                                 padding=15, style='Card.TFrame')
        dir_card.pack(fill="both", expand=True, pady=(0, 15))

        # Directory list with modern styling
        list_frame = ttk.Frame(dir_card)
        list_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Create listbox with modern scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.dir_listbox = tk.Listbox(list_frame, height=6,
                                     yscrollcommand=scrollbar.set,
                                     selectmode=tk.EXTENDED,
                                     font=('Segoe UI', 9),
                                     bg='#ffffff',
                                     fg='#1f2937',
                                     selectbackground='#2563eb',
                                     selectforeground='white',
                                     borderwidth=0,
                                     highlightthickness=1,
                                     highlightcolor='#2563eb')
        self.dir_listbox.pack(side=tk.LEFT, fill="both", expand=True)
        scrollbar.config(command=self.dir_listbox.yview)

        # Directory buttons with modern styling
        btn_frame = ttk.Frame(dir_card)
        btn_frame.pack(fill="x")

        ttk.Button(btn_frame, text="➕ Pridėti katalogą",
                  command=self.add_directory,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🗑️ Pašalinti pasirinktus",
                  command=self.remove_directories).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🧹 Išvalyti sąrašą",
                  command=self.clear_directories,
                  style='Warning.TButton').pack(side=tk.LEFT)

        # Action buttons card
        action_card = ttk.Frame(content_frame)
        action_card.pack(fill="x", pady=(0, 15))

        # Center the action buttons
        action_center = ttk.Frame(action_card)
        action_center.pack(anchor='center')

        self.process_btn = ttk.Button(
            action_center,
            text="🚀 Apdoroti Excel failus",
            command=self.process_files,
            style="Primary.TButton"
        )
        self.process_btn.pack(side=tk.LEFT, padx=(0, 15))

        self.export_btn = ttk.Button(
            action_center,
            text="📊 Eksportuoti į Excel",
            command=self.export_data,
            style="Success.TButton"
        )
        self.export_btn.pack(side=tk.LEFT)

        # Progress section
        progress_frame = ttk.Frame(content_frame)
        progress_frame.pack(fill="x", pady=(0, 15))

        progress_label = ttk.Label(progress_frame, text="Progresas:",
                                  style='Subheading.TLabel')
        progress_label.pack(anchor='w', pady=(0, 5))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=500,
            style='TProgressbar'
        )
        self.progress_bar.pack(fill="x")

        # Results summary card
        summary_card = ttk.LabelFrame(content_frame, text="📋 Apdorojimo rezultatai",
                                     padding=15, style='Card.TFrame')
        summary_card.pack(fill="both", expand=True)

        # Create text widget with modern styling
        text_frame = ttk.Frame(summary_card)
        text_frame.pack(fill="both", expand=True)

        self.summary_text = tk.Text(text_frame, height=6, wrap=tk.WORD,
                                   font=('Segoe UI', 9),
                                   bg='#f8fafc',
                                   fg='#1f2937',
                                   borderwidth=0,
                                   highlightthickness=1,
                                   highlightcolor='#e5e7eb',
                                   padx=10, pady=10)
        self.summary_text.pack(side=tk.LEFT, fill="both", expand=True)

        # Add modern scrollbar
        text_scrollbar = ttk.Scrollbar(text_frame, command=self.summary_text.yview)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.summary_text.config(yscrollcommand=text_scrollbar.set)

    def create_config_tab(self, parent):
        """Create modern configuration tab"""
        # Main content with padding
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_label = ttk.Label(content_frame, text="Konfigūracijos nustatymai",
                                style='Heading.TLabel')
        header_label.pack(anchor='w', pady=(0, 20))

        # Theme configuration card
        theme_card = ttk.LabelFrame(content_frame, text="📋 Temų koordinatės ir pavadinimai",
                                   padding=15, style='Card.TFrame')
        theme_card.pack(fill="both", expand=True, pady=(0, 15))

        # Create treeview with modern styling
        columns = ("Pavadinimas", "Rodyti", "Kairysis", "Dešinysis")
        self.tree = ttk.Treeview(theme_card, columns=columns, show="headings",
                                height=12, style='Modern.Treeview')

        # Configure columns
        self.tree.heading("Pavadinimas", text="Vidinis pavadinimas")
        self.tree.heading("Rodyti", text="Rodomas pavadinimas")
        self.tree.heading("Kairysis", text="Kairysis langelis")
        self.tree.heading("Dešinysis", text="Dešinysis langelis")

        self.tree.column("Pavadinimas", width=150)
        self.tree.column("Rodyti", width=250)
        self.tree.column("Kairysis", width=120)
        self.tree.column("Dešinysis", width=120)

        # Add modern scrollbar
        scrollbar = ttk.Scrollbar(theme_card, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        scrollbar.grid(row=0, column=1, sticky="ns")

        theme_card.grid_rowconfigure(0, weight=1)
        theme_card.grid_columnconfigure(0, weight=1)

        # Enable editing on double-click
        self.tree.bind("<Double-1>", self.on_double_click)

        # Button frame with modern styling
        btn_frame = ttk.Frame(content_frame)
        btn_frame.pack(fill="x", pady=(0, 15))

        ttk.Button(btn_frame, text="➕ Pridėti temą", command=self.add_theme,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🗑️ Pašalinti", command=self.remove_theme,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="⬆️ Aukštyn", command=lambda: self.move_theme(-1)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="⬇️ Žemyn", command=lambda: self.move_theme(1)).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Button(btn_frame, text="💾 Išsaugoti konfigūraciją", command=self.save_config,
                  style='Success.TButton').pack(side=tk.LEFT)

        # Additional settings card
        settings_card = ttk.LabelFrame(content_frame, text="⚙️ Papildomi nustatymai",
                                      padding=15, style='Card.TFrame')
        settings_card.pack(fill="x")

        # Create grid layout for settings
        settings_grid = ttk.Frame(settings_card)
        settings_grid.pack(fill="x")

        # Name cells configuration
        ttk.Label(settings_grid, text="Vardo langelis:",
                 style='Subheading.TLabel').grid(row=0, column=0, sticky="w", padx=(0, 10), pady=5)
        self.name_cell_var = tk.StringVar(value=self.config["name_cells"]["first_name"])
        ttk.Entry(settings_grid, textvariable=self.name_cell_var, width=12,
                 font=('Segoe UI', 9)).grid(row=0, column=1, padx=(0, 20), pady=5)

        ttk.Label(settings_grid, text="Pavardės langelis:",
                 style='Subheading.TLabel').grid(row=0, column=2, sticky="w", padx=(0, 10), pady=5)
        self.surname_cell_var = tk.StringVar(value=self.config["name_cells"]["last_name"])
        ttk.Entry(settings_grid, textvariable=self.surname_cell_var, width=12,
                 font=('Segoe UI', 9)).grid(row=0, column=3, padx=(0, 20), pady=5)

        ttk.Label(settings_grid, text="Klasės langelis:",
                 style='Subheading.TLabel').grid(row=1, column=0, sticky="w", padx=(0, 10), pady=5)
        self.class_cell_var = tk.StringVar(value=self.config.get("class_cell", ""))
        ttk.Entry(settings_grid, textvariable=self.class_cell_var, width=12,
                 font=('Segoe UI', 9)).grid(row=1, column=1, padx=(0, 20), pady=5)

        ttk.Label(settings_grid, text="Lygiagrečių procesų:",
                 style='Subheading.TLabel').grid(row=1, column=2, sticky="w", padx=(0, 10), pady=5)
        self.workers_var = tk.IntVar(value=self.config.get("max_workers", 4))
        ttk.Spinbox(settings_grid, from_=1, to=8, textvariable=self.workers_var, width=12,
                   font=('Segoe UI', 9)).grid(row=1, column=3, padx=(0, 20), pady=5)

    def create_preview_tab(self, parent):
        """Create modern results preview tab"""
        # Main content with padding
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_label = ttk.Label(content_frame, text="Rezultatų peržiūra",
                                style='Heading.TLabel')
        header_label.pack(anchor='w', pady=(0, 20))

        # Folder selection card
        selection_card = ttk.LabelFrame(content_frame, text="📁 Katalogo pasirinkimas",
                                       padding=15, style='Card.TFrame')
        selection_card.pack(fill="x", pady=(0, 15))

        folder_frame = ttk.Frame(selection_card)
        folder_frame.pack(fill="x")

        ttk.Label(folder_frame, text="Peržiūrėti katalogą:",
                 style='Subheading.TLabel').pack(side=tk.LEFT, padx=(0, 10))
        self.preview_folder_var = tk.StringVar()
        self.preview_folder_combo = ttk.Combobox(folder_frame, textvariable=self.preview_folder_var,
                                                width=50, font=('Segoe UI', 9))
        self.preview_folder_combo.pack(side=tk.LEFT, fill="x", expand=True)
        self.preview_folder_combo.bind("<<ComboboxSelected>>", self.on_preview_folder_changed)

        # Results preview card
        preview_card = ttk.LabelFrame(content_frame, text="📊 Duomenų peržiūra",
                                     padding=15, style='Card.TFrame')
        preview_card.pack(fill="both", expand=True, pady=(0, 15))

        # Create treeview for results with modern styling
        self.results_tree = ttk.Treeview(preview_card, show="headings", height=15,
                                        style='Modern.Treeview')

        # Add modern scrollbars
        y_scrollbar = ttk.Scrollbar(preview_card, orient="vertical", command=self.results_tree.yview)
        x_scrollbar = ttk.Scrollbar(preview_card, orient="horizontal", command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=y_scrollbar.set, xscrollcommand=x_scrollbar.set)

        # Grid layout
        self.results_tree.grid(row=0, column=0, sticky="nsew", padx=(0, 5), pady=(0, 5))
        y_scrollbar.grid(row=0, column=1, sticky="ns", pady=(0, 5))
        x_scrollbar.grid(row=1, column=0, sticky="ew", padx=(0, 5))

        preview_card.grid_rowconfigure(0, weight=1)
        preview_card.grid_columnconfigure(0, weight=1)

        # Export button with modern styling
        export_frame = ttk.Frame(content_frame)
        export_frame.pack(fill="x")

        ttk.Button(export_frame, text="📊 Eksportuoti pasirinktus",
                  command=self.export_selected,
                  style='Success.TButton').pack(anchor='center')

    def on_double_click(self, event):
        """Handle double-click on treeview item for editing"""
        item = self.tree.selection()[0]
        column = self.tree.identify_column(event.x)

        if column:
            col_index = int(column.replace('#', '')) - 1

            # Get current value
            values = list(self.tree.item(item, 'values'))
            current_value = values[col_index]

            # Create entry widget for editing
            x, y, width, height = self.tree.bbox(item, column)

            entry = ttk.Entry(self.tree)
            entry.place(x=x, y=y, width=width, height=height)
            entry.insert(0, current_value)
            entry.focus()
            entry.select_range(0, tk.END)

            def save_edit(event=None):
                values[col_index] = entry.get()
                self.tree.item(item, values=values)
                entry.destroy()
                if self.config.get("auto_save", True):
                    self.save_config()

            def cancel_edit(event=None):
                entry.destroy()

            entry.bind('<Return>', save_edit)
            entry.bind('<Escape>', cancel_edit)
            entry.bind('<FocusOut>', save_edit)

    def load_themes_to_tree(self):
        """Load themes from config to treeview"""
        for theme in self.config["themes"]:
            self.tree.insert("", "end", values=(
                theme["label"],
                theme.get("display_name", theme["label"]),
                theme["left_cell"],
                theme.get("right_cell", "")
            ))

    def save_config(self):
        """Save current configuration"""
        # Update themes from tree
        self.config["themes"] = []
        for item in self.tree.get_children():
            label, display_name, left, right = self.tree.item(item)["values"]
            if label and left:
                self.config["themes"].append({
                    "label": label,
                    "display_name": display_name or label,
                    "left_cell": left,
                    "right_cell": right or ""
                })

        # Update other settings
        self.config["name_cells"]["first_name"] = self.name_cell_var.get()
        self.config["name_cells"]["last_name"] = self.surname_cell_var.get()
        self.config["class_cell"] = self.class_cell_var.get()
        self.config["max_workers"] = self.workers_var.get()
        self.config["last_directories"] = self.selected_directories

        # Save to file
        self.config_manager.save(self.config)
        self.processor.config = self.config
        self.update_status("Konfigūracija išsaugota")

    def add_directory(self):
        """Add directory to the list"""
        directory = filedialog.askdirectory()
        if directory and directory not in self.selected_directories:
            self.selected_directories.append(directory)
            self.update_directory_list()

    def remove_directories(self):
        """Remove selected directories from the list"""
        selected_indices = self.dir_listbox.curselection()
        for index in reversed(selected_indices):
            del self.selected_directories[index]
        self.update_directory_list()

    def clear_directories(self):
        """Clear all directories"""
        self.selected_directories.clear()
        self.update_directory_list()

    def update_directory_list(self):
        """Update the directory listbox"""
        self.dir_listbox.delete(0, tk.END)
        for directory in self.selected_directories:
            self.dir_listbox.insert(tk.END, directory)

    def process_files(self):
        """Process Excel files in selected directories with modern UI feedback"""
        if not self.selected_directories:
            messagebox.showerror("⚠️ Klaida", "Pirmiausia pasirinkite bent vieną katalogą.")
            return

        if self.processing:
            messagebox.showinfo("ℹ️ Informacija", "Apdorojimas jau vyksta.")
            return

        # Save config before processing
        self.save_config()

        # Clear previous results
        self.summary_text.delete(1.0, tk.END)
        self.progress_var.set(0)

        # Start processing in thread
        self.processing = True
        self.process_btn.config(state="disabled", text="🔄 Apdorojama...")
        self.update_status("🚀 Pradedamas apdorojimas...")

        thread = threading.Thread(target=self._process_files_thread, daemon=True)
        thread.start()

    def _process_files_thread(self):
        """Process files in separate thread with performance tracking"""
        def update_progress(current, total):
            progress = (current / total) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.update_status(f"📊 Apdorota {current}/{total} failų ({progress:.1f}%)"))

        try:
            start_time = time.time()
            # Process files
            results_by_folder, all_results, errors = self.processor.process_directories(
                self.selected_directories, update_progress
            )
            end_time = time.time()
            processing_time = end_time - start_time

            # Update UI in main thread
            self.root.after(0, lambda: self._process_complete(results_by_folder, all_results, errors, processing_time))
        except Exception as e:
            self.root.after(0, lambda: self._process_error(str(e)))

    def _process_complete(self, results_by_folder, all_results, errors, processing_time=None):
        """Handle processing completion with modern formatting"""
        self.processing = False
        self.process_btn.config(state="normal", text="🚀 Apdoroti Excel failus")

        # Update summary with modern formatting
        self.summary_text.delete(1.0, tk.END)

        # Header with emoji and modern formatting
        self.summary_text.insert(tk.END, "📊 APDOROJIMO SUVESTINĖ\n", "header")
        self.summary_text.insert(tk.END, f"{'='*60}\n\n", "separator")

        # Performance metrics
        total_files = len(all_results)
        total_folders = len(results_by_folder)

        if processing_time:
            files_per_second = total_files / processing_time if processing_time > 0 else 0
            self.summary_text.insert(tk.END, f"⚡ Apdorojimo laikas: {processing_time:.2f} sek.\n", "metric")
            self.summary_text.insert(tk.END, f"🚀 Greitis: {files_per_second:.1f} failų/sek.\n", "metric")
            self.summary_text.insert(tk.END, "\n")

        # Summary statistics
        self.summary_text.insert(tk.END, f"📁 Iš viso apdorota failų: {total_files}\n", "success")
        self.summary_text.insert(tk.END, f"📂 Katalogų: {total_folders}\n", "info")
        self.summary_text.insert(tk.END, f"❌ Klaidų: {len(errors)}\n\n", "error" if errors else "success")

        # Results by folder with better formatting
        if results_by_folder:
            self.summary_text.insert(tk.END, "📋 REZULTATAI PAGAL KATALOGUS:\n", "subheader")
            self.summary_text.insert(tk.END, f"{'-'*40}\n", "separator")
            for folder, results in sorted(results_by_folder.items()):
                status_icon = "✅" if results else "⚠️"
                self.summary_text.insert(tk.END, f"{status_icon} {folder}: {len(results)} failų\n", "folder")

        # Errors section with better visibility
        if errors:
            self.summary_text.insert(tk.END, f"\n🚨 KLAIDOS ({len(errors)}):\n", "error_header")
            self.summary_text.insert(tk.END, f"{'-'*30}\n", "separator")
            for error in errors[:10]:  # Show first 10 errors
                self.summary_text.insert(tk.END, f"• {error}\n", "error_detail")
            if len(errors) > 10:
                self.summary_text.insert(tk.END, f"... ir dar {len(errors) - 10} klaidų\n", "error_detail")

        # Configure text tags for better formatting
        self._configure_text_tags()

        # Update preview combo
        folder_names = ["Visi"] + list(results_by_folder.keys())
        self.preview_folder_combo['values'] = folder_names
        if folder_names:
            self.preview_folder_combo.set(folder_names[0])
            self.update_results_preview("Visi")

        # Show completion message with modern styling
        if all_results:
            success_msg = f"✅ Sėkmingai apdorota {len(all_results)} failų iš {len(results_by_folder)} katalogų"
            if processing_time:
                success_msg += f" per {processing_time:.1f} sek."
            messagebox.showinfo("🎉 Baigta", success_msg)
            self.update_status(f"✅ Baigta: {len(all_results)} failų apdorota")
        else:
            self.update_status("⚠️ Nerastas nei vienas tinkamas failas")

    def _process_error(self, error_msg):
        """Handle processing errors"""
        self.processing = False
        self.process_btn.config(state="normal", text="🚀 Apdoroti Excel failus")
        messagebox.showerror("❌ Klaida", f"Apdorojimo klaida: {error_msg}")
        self.update_status("❌ Apdorojimas nutrauktas dėl klaidos")
        logger.error(f"Processing error: {error_msg}")

    def _configure_text_tags(self):
        """Configure text tags for modern formatting"""
        self.summary_text.tag_configure("header", font=('Segoe UI', 12, 'bold'), foreground='#1f2937')
        self.summary_text.tag_configure("subheader", font=('Segoe UI', 10, 'bold'), foreground='#374151')
        self.summary_text.tag_configure("separator", foreground='#9ca3af')
        self.summary_text.tag_configure("success", foreground='#059669', font=('Segoe UI', 9, 'bold'))
        self.summary_text.tag_configure("info", foreground='#2563eb', font=('Segoe UI', 9))
        self.summary_text.tag_configure("error", foreground='#dc2626', font=('Segoe UI', 9, 'bold'))
        self.summary_text.tag_configure("error_header", foreground='#dc2626', font=('Segoe UI', 10, 'bold'))
        self.summary_text.tag_configure("error_detail", foreground='#dc2626', font=('Segoe UI', 9))
        self.summary_text.tag_configure("metric", foreground='#7c3aed', font=('Segoe UI', 9))
        self.summary_text.tag_configure("folder", foreground='#374151', font=('Segoe UI', 9))

    def on_preview_folder_changed(self, event=None):
        """Handle preview folder selection change"""
        selected_folder = self.preview_folder_var.get()
        self.update_results_preview(selected_folder)

    def update_results_preview(self, folder_name):
        """Update results preview treeview"""
        # Clear existing items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Get appropriate results
        if folder_name == "Visi":
            results = self.processor.all_results
        else:
            results = self.processor.results_by_folder.get(folder_name, {})

        if not results:
            return

        # Convert to DataFrame for sorting, keeping original index
        df = pd.DataFrame.from_dict(results, orient='index')
        df['original_key'] = df.index  # Store original keys

        # Apply Lithuanian sorting
        if folder_name == "Visi":
            df = self.sort_dataframe_lithuanian(df, by_class=True)
        else:
            df = self.sort_dataframe_lithuanian(df, by_class=False)

        # Get columns (excluding our helper column)
        columns = [col for col in df.columns if col != 'original_key']

        # Configure columns
        self.results_tree["columns"] = columns
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120)

        # Add data
        for idx, row in df.iterrows():
            values = [row[col] for col in columns]
            original_key = row['original_key']
            self.results_tree.insert("", "end", values=values, tags=(original_key,))

    def lithuanian_sort_key(self, text):
        """Create sort key for Lithuanian alphabetical order"""
        # Lithuanian alphabet order
        lithuanian_order = {
            'a': '01', 'ą': '02', 'b': '03', 'c': '04', 'č': '05', 'd': '06', 'e': '07', 'ę': '08', 'ė': '09',
            'f': '10', 'g': '11', 'h': '12', 'i': '13', 'į': '14', 'y': '15', 'j': '16', 'k': '17',
            'l': '18', 'ł': '19', 'm': '20', 'n': '21', 'o': '22', 'p': '23', 'r': '24', 's': '25',
            'š': '26', 't': '27', 'u': '28', 'ų': '29', 'ū': '30', 'v': '31', 'z': '32', 'ž': '33'
        }

        result = []
        for char in str(text).lower():
            if char in lithuanian_order:
                result.append(lithuanian_order[char])
            else:
                # For non-Lithuanian characters, use Unicode value
                result.append(f"{ord(char):04d}")

        return ''.join(result)

    def sort_dataframe_lithuanian(self, df, by_class=False):
        """Sort DataFrame using Lithuanian alphabetical order"""
        # Create a copy to avoid modifying the original
        df = df.copy()

        if by_class:
            # Create sort keys for class, surname, and name
            df['_sort_class'] = df['Klasė'].fillna('').apply(self.lithuanian_sort_key)
            df['_sort_surname'] = df['Pavardė'].fillna('').apply(self.lithuanian_sort_key)
            df['_sort_name'] = df['Vardas'].fillna('').apply(self.lithuanian_sort_key)

            # Sort by all three keys
            df = df.sort_values(by=['_sort_class', '_sort_surname', '_sort_name'])

            # Remove temporary columns
            df = df.drop(columns=['_sort_class', '_sort_surname', '_sort_name'])
        else:
            # Create sort keys for surname and name
            df['_sort_surname'] = df['Pavardė'].fillna('').apply(self.lithuanian_sort_key)
            df['_sort_name'] = df['Vardas'].fillna('').apply(self.lithuanian_sort_key)

            # Sort by surname and name
            df = df.sort_values(by=['_sort_surname', '_sort_name'])

            # Remove temporary columns
            df = df.drop(columns=['_sort_surname', '_sort_name'])

        return df

    def create_exact_format_export(self, worksheet, df):
        """Apply the exact format from the example file with dynamic headers and bottom counts"""
        # Build headers dynamically from configuration
        base_headers = ["Nr.", "Vardas", "Pavardė", "Klasė"]

        # Get theme headers from configuration in order
        theme_headers = []
        for theme in self.config["themes"]:
            theme_headers.append(theme.get("display_name", theme["label"]))

        # Complete headers list
        headers = base_headers + theme_headers + ["Suma", "Parašas"]

        # Write headers with proper rotation for subject columns
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx)
            cell.value = header

            # Apply rotation to subject columns (columns 5 onwards, excluding Suma and Parašas)
            if col_idx >= 5 and col_idx <= len(headers) - 2:
                cell.alignment = Alignment(text_rotation=90, horizontal="center", vertical="bottom", wrap_text=True)
            else:
                cell.alignment = Alignment(horizontal="center", vertical="bottom", wrap_text=True)

        # Set dynamic column widths
        exact_widths = {
            'A': 6,          # Nr.
            'B': 10.7,       # Vardas
            'C': 16.1,       # Pavardė
            'D': 8,          # Klasė
        }

        # Subject columns - narrow width for all theme columns
        theme_start_col = 5  # Column E
        for i in range(len(theme_headers)):
            col_letter = get_column_letter(theme_start_col + i)
            exact_widths[col_letter] = 4.3

        # Special columns (Suma and Parašas)
        suma_col = get_column_letter(theme_start_col + len(theme_headers))
        paras_col = get_column_letter(theme_start_col + len(theme_headers) + 1)
        exact_widths[suma_col] = 5.9  # Suma
        exact_widths[paras_col] = 8   # Parašas

        # Apply column widths
        for col, width in exact_widths.items():
            worksheet.column_dimensions[col].width = width

        # Set header row height (convert from points to Excel units)
        worksheet.row_dimensions[1].height = 189.75  # 180 points = 135 Excel units

        # Write data
        data_end_row = 1  # Track where data ends
        for row_idx, (_, row_data) in enumerate(df.iterrows(), 2):
            data_end_row = row_idx

            # Nr.
            worksheet.cell(row=row_idx, column=1).value = row_idx - 1

            # Basic info
            worksheet.cell(row=row_idx, column=2).value = row_data.get('Vardas', '')
            worksheet.cell(row=row_idx, column=3).value = row_data.get('Pavardė', '')
            worksheet.cell(row=row_idx, column=4).value = row_data.get('Klasė', '')

            # Theme subjects - write values from the processed data
            col_idx = 5
            suma_range_start = get_column_letter(5)
            suma_range_end = get_column_letter(4 + len(theme_headers))

            for theme_header in theme_headers:
                value = None
                if theme_header in row_data.index:
                    value = row_data[theme_header]
                    if pd.notna(value) and value != '' and value != 0:
                        worksheet.cell(row=row_idx, column=col_idx).value = value
                col_idx += 1

            # Add SUM formula in "Suma" column
            suma_col_idx = 4 + len(theme_headers) + 1
            suma_cell = worksheet.cell(row=row_idx, column=suma_col_idx)
            suma_cell.value = f"=SUM({suma_range_start}{row_idx}:{suma_range_end}{row_idx})"

            # Leave "Parašas" column empty

        # Add count totals at the bottom (like in the example)
        self.add_bottom_counts(worksheet, headers, data_end_row + 2)

        # Apply styling
        self.apply_exact_styling(worksheet, data_end_row + 3)

    def add_bottom_counts(self, worksheet, headers, count_row):
        """Add count totals at the bottom of the worksheet like in the example"""
        # Skip the first 4 columns (Nr., Vardas, Pavardė, Klasė) and last 2 (Suma, Parašas)
        theme_start_col = 5
        theme_end_col = len(headers) - 2  # Exclude Suma and Parašas

        # Add the count formulas in the row below
        count_formula_row = count_row + 1
        for col_idx in range(theme_start_col, theme_end_col + 1):
            col_letter = get_column_letter(col_idx)
            count_cell = worksheet.cell(row=count_formula_row, column=col_idx)
            # Count non-empty cells in this column (excluding header and count rows)
            count_cell.value = f"=COUNTA({col_letter}2:{col_letter}{count_row - 1})"
            count_cell.alignment = Alignment(horizontal="center", vertical="center")
            count_cell.font = Font(size=10, bold=True)

    def apply_exact_styling(self, worksheet, max_data_row=None):
        """Apply exact styling to match the example file"""
        # Define styles
        header_font = Font(bold=True, size=11)
        normal_font = Font(size=11)

        # Define border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Determine the actual max column and row
        max_col = worksheet.max_column
        if max_data_row is None:
            max_row = worksheet.max_row
        else:
            max_row = max_data_row

        # Style headers (preserve existing alignment set during header creation)
        for col in range(1, max_col + 1):
            header_cell = worksheet.cell(row=1, column=col)
            header_cell.font = header_font
            # Don't override alignment if it's already set (for rotated headers)
            if header_cell.alignment.text_rotation is None:
                header_cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            header_cell.border = thin_border

        # Style data rows (excluding count rows at bottom)
        data_end_row = max_data_row - 2 if max_data_row else max_row
        for row in range(2, data_end_row):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.font = normal_font
                cell.border = thin_border

                # Set alignment based on column
                if col == 1:  # Nr. column - center
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                elif col in [2, 3, 4]:  # Name columns - left
                    cell.alignment = Alignment(horizontal="left", vertical="center")
                elif col == max_col:  # Parašas column (last column) - center
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                else:  # Subject columns and Suma - center
                    cell.alignment = Alignment(horizontal="center", vertical="center")

        # Style count rows at bottom if they exist
        if max_data_row:
            count_start_row = max_data_row - 1
            for row in range(count_start_row, max_data_row + 1):
                for col in range(1, max_col + 1):
                    cell = worksheet.cell(row=row, column=col)
                    if cell.value:  # Only style cells with content
                        cell.border = thin_border

        # Freeze panes at A2 (freeze header row)
        worksheet.freeze_panes = 'A2'

    def export_data(self):
        """Export processed data to Excel with exact format matching"""
        if not self.processor.all_results:
            messagebox.showinfo("Informacija", "Nėra duomenų eksportavimui.")
            return

        # Ask for file location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel failai", "*.xlsx")],
            initialfile=f"planai_{datetime.now().strftime('%Y_%m_%d_%H%M%S')}.xlsx"
        )

        if not file_path:
            return

        try:
            # Create workbook
            wb = Workbook()

            # First sheet - "Bendras" (all students combined, sorted by class then surname)
            ws_bendras = wb.active
            ws_bendras.title = "Bendras"

            all_df = pd.DataFrame.from_dict(self.processor.all_results, orient='index')
            all_df = self.sort_dataframe_lithuanian(all_df, by_class=True)

            self.create_exact_format_export(ws_bendras, all_df)

            # Create separate sheets for each folder (class)
            for folder_name, results in sorted(self.processor.results_by_folder.items()):
                if results:
                    # Create new worksheet
                    ws = wb.create_sheet(title=folder_name[:31])  # Excel sheet name limit

                    df = pd.DataFrame.from_dict(results, orient='index')
                    df = self.sort_dataframe_lithuanian(df, by_class=False)

                    self.create_exact_format_export(ws, df)

            # Save workbook
            wb.save(file_path)

            messagebox.showinfo("Sėkmė", f"Duomenys sėkmingai eksportuoti į:\n{file_path}\n\nFailas turi tikslų formatą kaip pavyzdyje su:\n• Dinaminiais antraštės pavadinimais\n• Formulėmis ir dizainu\n• Skaičiavimais apačioje kiekvienai kolonai")
            self.update_status("Duomenys eksportuoti su tiksliu formatu ir skaičiavimais")

        except Exception as e:
            messagebox.showerror("Klaida", f"Nepavyko eksportuoti: {str(e)}")
            logger.error(f"Export error: {e}")

    def export_selected(self):
        """Export only selected results with exact format"""
        selected = self.results_tree.selection()
        if not selected:
            messagebox.showinfo("Informacija", "Pasirinkite įrašus eksportavimui.")
            return

        # Get current folder
        current_folder = self.preview_folder_var.get()

        # Get selected data
        selected_results = {}
        for item in selected:
            tag = self.results_tree.item(item)["tags"][0]
            if current_folder == "Visi":
                if tag in self.processor.all_results:
                    selected_results[tag] = self.processor.all_results[tag]
            else:
                folder_results = self.processor.results_by_folder.get(current_folder, {})
                if tag in folder_results:
                    selected_results[tag] = folder_results[tag]

        if not selected_results:
            return

        # Ask for file location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel failai", "*.xlsx")],
            initialfile=f"pasirinkti_planai_{datetime.now().strftime('%Y_%m_%d_%H%M%S')}.xlsx"
        )

        if not file_path:
            return

        try:
            df = pd.DataFrame.from_dict(selected_results, orient='index')
            # Use Lithuanian sorting - by class if from "Visi", otherwise just by name
            if current_folder == "Visi":
                df = self.sort_dataframe_lithuanian(df, by_class=True)
            else:
                df = self.sort_dataframe_lithuanian(df, by_class=False)

            # Create workbook with exact format
            wb = Workbook()
            ws = wb.active
            ws.title = "Pasirinkti mokiniai"

            self.create_exact_format_export(ws, df)

            wb.save(file_path)

            messagebox.showinfo("Sėkmė", f"Pasirinkti duomenys eksportuoti į:\n{file_path}\n\nSu tiksliu formatu:\n• Dinaminiais antraštės pavadinimais\n• Formulėmis ir dizainu\n• Skaičiavimais apačioje kiekvienai kolonai")

        except Exception as e:
            messagebox.showerror("Klaida", f"Nepavyko eksportuoti: {str(e)}")

    def add_theme(self):
        """Add new theme"""
        self.tree.insert("", "end", values=("Nauja", "Nauja tema", "", ""))

    def remove_theme(self):
        """Remove selected theme"""
        selected = self.tree.selection()
        for item in selected:
            self.tree.delete(item)

        if self.config.get("auto_save", True):
            self.save_config()

    def move_theme(self, direction):
        """Move selected theme up or down"""
        selected = self.tree.selection()
        if not selected:
            return

        item = selected[0]
        index = self.tree.index(item)
        parent = self.tree.parent(item)

        if direction == -1 and index > 0:  # Move up
            self.tree.move(item, parent, index - 1)
        elif direction == 1 and index < len(self.tree.get_children()) - 1:  # Move down
            self.tree.move(item, parent, index + 1)

        if self.config.get("auto_save", True):
            self.save_config()

    def update_status(self, message):
        """Update status bar"""
        self.status_bar.config(text=message)

    def on_closing(self):
        """Handle window closing"""
        if self.processing:
            if messagebox.askokcancel("Uždaryti", "Apdorojimas dar vyksta. Ar tikrai norite uždaryti?"):
                self.root.destroy()
        else:
            self.save_config()
            self.root.destroy()


def main():
    """Main application entry point with modern styling"""
    root = tk.Tk()

    # Set window icon and properties
    try:
        root.iconbitmap(default='')  # Remove default icon
    except:
        pass

    # Create and run application
    app = ExcelProcessorApp(root)

    # Load last directories if any
    if app.config.get("last_directories"):
        app.selected_directories = app.config["last_directories"]
        app.update_directory_list()

    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()


if __name__ == "__main__":
    main()
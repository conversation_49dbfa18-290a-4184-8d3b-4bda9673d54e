import os
import pandas as pd
from openpyxl import load_workbook
from tkinter import Tk, filedialog
import locale

def select_directory():
    root = Tk()
    root.withdraw()
    directory = filedialog.askdirectory(title="Select Directory")
    root.destroy()
    return directory

def read_excel_files(directory):
    data = []
    grade = os.path.basename(directory)
    for filename in os.listdir(directory):
        if filename.endswith(".xlsx"):
            filepath = os.path.join(directory, filename)
            wb = load_workbook(filepath, data_only=True)
            sheet = wb.active
            name = sheet["D4"].value
            surname = sheet["J4"].value
            rest_of_data = [sheet[cell].value for cell in ["N9", "N10", "N11", "N12", "N13", "N14", "N15", 
                                                           "N16", "N18", "N21", "N22", "N23", "N24", "N25", 
                                                           "N26", "N27", "N28", "G31", "P31", "G32", "G35", 
                                                           "P35", "G36", "P36", "G37", "P37"]]
            data.append(["", name, surname, grade] + rest_of_data)
    return data

def combine_and_sort_data(directory):
    data = read_excel_files(directory)
    df = pd.DataFrame(data, columns=["Numeris", "Vardas", "Pavardė", "Klasė", "Etika", "Tikyba", "Lietuvių kalba", "Matematika",
                                     "Fizinis", "Dailė", "Muzika", "Šokis", "Technologijos",  "Anglų", "Biologija", "Chemija",
                                     "Fizika", "Informatika", "Istorija", "Geografija", "Ekonomika", "Rusų", "Vokiečių", "Prancūzų",
                                     "MBiologija", "MChemija", "MFizika", "MGeografija", "MIstorija", "MLietuvių"])
    
    df['Vardas'] = df['Vardas'].apply(lambda x: x.title())
    df['Pavardė'] = df['Pavardė'].apply(lambda x: x.title())
    
    locale.setlocale(locale.LC_COLLATE, 'lt_LT.UTF-8') # Set Lithuanian locale for sorting
    
    # Define a custom sorting key function
    def lithuanian_sort_key(series):
        return series.apply(lambda x: locale.strxfrm(str(x)))
    
    # Sort using the custom key function
    df_sorted = df.sort_values(by=["Pavardė", "Vardas"], key=lithuanian_sort_key)
    df_sorted["Numeris"] = range(1, len(df_sorted) + 1)
    return df_sorted


def save_to_excel(df, directory, output_filename):
    parent_directory = os.path.dirname(directory)
    output_filepath = os.path.join(parent_directory, output_filename + ".xlsx")
    df.to_excel(output_filepath, index=False)

if __name__ == "__main__":
    directory = select_directory()
    if not directory:
        print("No directory selected. Exiting.")
    else:
        output_filename = os.path.basename(directory)
        sorted_data = combine_and_sort_data(directory)
        save_to_excel(sorted_data, directory, output_filename)
        print("Data saved to", output_filename)

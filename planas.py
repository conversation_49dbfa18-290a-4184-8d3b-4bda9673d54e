import os
import pandas as pd
from reportlab.pdfgen import canvas
from tkinter import Tk, filedialog, Label, But<PERSON>

def process_csv(input_folder, output_folder):
    # List of exams
    exams = [
        "Lietuvių kalba ir literatūra",
        "Gim<PERSON><PERSON> kalba (rusų)",
        "<PERSON><PERSON><PERSON> tautinės mažumos gimtoji kalba ir literatūra",
        "<PERSON><PERSON><PERSON><PERSON> kal<PERSON> (baltarusių)",
        "<PERSON><PERSON><PERSON><PERSON> (vokiečių)",
        "Užsienio kalba (anglų)",
        "Istorija",
        "Matematika",
        "Biologija",
        "Fizika",
        "Chemija",
        "Informacinės technologijos",
        "Geografija",
        "Muzikologija",
        "Menai",
        "Užsienio kalba (rusų)",
        "Užsienio kalba (prancūzų)",
        "Užsienio kalba (vokiečių)",
        "Technologijos"
    ]

    for filename in os.listdir(input_folder):
        if filename.endswith(".csv"):
            filepath = os.path.join(input_folder, filename)
            df = pd.read_csv(filepath, skiprows=2)
            group_name = df.columns[3].split("Grupė:")[1].strip()

            exam_arrays = {exam: [] for exam in exams}

            for i in range(6, len(df), 21):
                block = df.iloc[i:i + 20]
                name = block.iloc[0, 3]
                for j in range(19):
                    if len(block.iloc[j]) >= 7:
                        if block.iloc[j, 7] == 'V':
                            exam_arrays[exams[j]].append(f"{name} ({group_name})")

            for exam, names in exam_arrays.items():
                if names:
                    pdf_path = os.path.join(output_folder, f"{exam}.pdf")
                    generate_pdf(pdf_path, exam, names)

def generate_pdf(pdf_path, exam, names):
    c = canvas.Canvas(pdf_path)
    c.drawString(100, 800, f"Exam: {exam}")
    c.drawString(100, 780, "Participants:")
    y_position = 760
    for name in names:
        c.drawString(120, y_position, name)
        y_position -= 20
    c.save()

def select_folders():
    root = Tk()
    root.withdraw()
    input_folder = filedialog.askdirectory(title="Select input folder")
    output_folder = filedialog.askdirectory(title="Select output folder")
    root.destroy()
    return input_folder, output_folder

if __name__ == "__main__":
    input_folder, output_folder = select_folders()
    process_csv(input_folder, output_folder)

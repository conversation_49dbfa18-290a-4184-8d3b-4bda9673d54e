import os
import pandas as pd
from tkinter import Tk, filedialog

def select_directory():
    root = Tk()
    root.withdraw()
    directory = filedialog.askdirectory(title="Select Directory")
    root.destroy()
    return directory

def combine_and_sort_data(directory):
    files = [file for file in os.listdir(directory) if file.endswith('.xlsx')]
    dfs = []
    for file in files:
        filepath = os.path.join(directory, file)
        df = pd.read_excel(filepath, usecols="D,J,N:P", header=None, names=["Vardas", "Pavardė"] + [f"Klasė_{i+1}" for i in range(28)])
        df['Numeris'] = range(1, len(df) + 1)
        dfs.append(df)
    combined_df = pd.concat(dfs, ignore_index=True)
    combined_df['Vardas'] = combined_df['Vardas'].str.title()
    combined_df['Pavardė'] = combined_df['Pavardė'].str.title()
    sorted_df = combined_df.sort_values(by=["<PERSON><PERSON>ė", "Vardas"])
    return sorted_df

def save_to_excel(df, directory, output_filename):
    output_filepath = os.path.join(directory, output_filename + ".xlsx")
    df.to_excel(output_filepath, index=False)

if __name__ == "__main__":
    directory = select_directory()
    if not directory:
        print("No directory selected. Exiting.")
    else:
        output_filename = os.path.basename(directory)
        sorted_data = combine_and_sort_data(directory)
        save_to_excel(sorted_data, directory, output_filename)
        print("Data saved to", output_filename)

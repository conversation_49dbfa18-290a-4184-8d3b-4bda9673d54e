import os
import tkinter as tk
from zipfile import BadZipFile
from tkinter import filedialog, ttk, messagebox
from openpyxl import load_workbook
from openpyxl.utils.exceptions import InvalidFileException
import pandas as pd


class ExcelProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel duomenų apdorojimas")
        self.root.geometry("900x600")
        self.root.configure(padx=20, pady=20)

        # Kintamieji
        self.katalogo_kelias = tk.StringVar()
        self.rezultatai = {}
        self.temu_mappings = []

        # Sukurti sąsajos elementus
        self.sukurti_valdiklius()

        # Pradiniai duomenys
        self.prideti_pradinius_mappings()

    def sukurti_valdiklius(self):
        # Katalogo pasirinkimas
        frame = ttk.LabelFrame(self.root, text="Pasirinkite katalogą su Excel failais")
        frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(frame, text="Katalogas:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(frame, textvariable=self.katalogo_kelias, width=50).pack(side=tk.LEFT, fill="x", expand=True)
        ttk.Button(frame, text="Naršyti...", command=self.pasirinkti_kataloga).pack(side=tk.LEFT, padx=5)

        # Mappings redagavimas
        self.temu_frame = ttk.LabelFrame(self.root, text="Temų koordinatės ir pavadinimai")
        self.temu_frame.pack(fill="both", expand=False, padx=10, pady=10)

        self.tree = ttk.Treeview(self.temu_frame, columns=("Pavadinimas", "Kairysis", "Dešinysis"), show="headings")
        self.tree.heading("Pavadinimas", text="Tema")
        self.tree.heading("Kairysis", text="Kairysis langelis")
        self.tree.heading("Dešinysis", text="Dešinysis langelis")
        self.tree.pack(side=tk.LEFT, fill="both", expand=True)

        scrollbar = ttk.Scrollbar(self.temu_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill="y")

        btns = ttk.Frame(self.root)
        btns.pack(pady=5)

        ttk.Button(btns, text="Pridėti temą", command=self.prideti_tema).pack(side=tk.LEFT, padx=5)
        ttk.Button(btns, text="Pašalinti pasirinktą", command=self.pasalinti_tema).pack(side=tk.LEFT, padx=5)
        ttk.Button(btns, text="Perkelti aukštyn", command=lambda: self.perkelti_tema(-1)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btns, text="Perkelti žemyn", command=lambda: self.perkelti_tema(1)).pack(side=tk.LEFT, padx=5)

        # Veiksmo mygtukai
        ttk.Button(self.root, text="Apdoroti Excel failus", command=self.apdoroti_failus).pack(pady=5)
        ttk.Button(self.root, text="Eksportuoti į Excel", command=self.eksportuoti_duomenis).pack(pady=5)

    def pasirinkti_kataloga(self):
        pasirinktas = filedialog.askdirectory()
        if pasirinktas:
            self.katalogo_kelias.set(pasirinktas)

    def prideti_pradinius_mappings(self):
        pradines_temos = [
            ("Etika", "N9"), ("Tikyba", "N10"), ("Lietuvių kalba", "N11"), ("Matematika", "N12"),
            ("Fizinis", "N13"), ("Dailė", "N14"), ("Muzika", "N15"), ("Šokis", "N16"),
            ("Technologijos", "N18"), ("Anglų", "N21"), ("Biologija", "N22"), ("Chemija", "N23"),
            ("Fizika", "N24"), ("Informatika", "N25"), ("Istorija", "N26"), ("Geografija", "N27"),
            ("Ekonomika", "N28"), ("Rusų", "G31"), ("Vokiečių", "P31"), ("Prancūzų", "G32"),
            ("MBiologija", "G35"), ("MChemija", "P35"), ("MFizika", "G36"), ("MGeografija", "P36"),
            ("MIstorija", "G37"), ("MLietuvių", "P37"),
        ]
        for tema, langelis in pradines_temos:
            self.temu_mappings.append({"label": tema, "left_cell": langelis})
            self.tree.insert("", "end", values=(tema, langelis, ""))

    def prideti_tema(self):
        self.tree.insert("", "end", values=("", "", ""))
        self.temu_mappings.append({"label": "", "left_cell": "", "right_cell": ""})

    def pasalinti_tema(self):
        selected = self.tree.selection()
        for item in selected:
            index = self.tree.index(item)
            self.tree.delete(item)
            del self.temu_mappings[index]

    def perkelti_tema(self, direction):
        selected = self.tree.selection()
        if not selected:
            return
        index = self.tree.index(selected[0])
        new_index = index + direction
        if 0 <= new_index < len(self.temu_mappings):
            self.temu_mappings[index], self.temu_mappings[new_index] = self.temu_mappings[new_index], self.temu_mappings[index]
            values = [self.tree.item(item)["values"] for item in self.tree.get_children()]
            self.tree.delete(*self.tree.get_children())
            for row in values:
                self.tree.insert("", "end", values=row)

    def nuskaityti_mappings_is_tree(self):
        self.temu_mappings.clear()
        for item in self.tree.get_children():
            label, left, right = self.tree.item(item)["values"]
            if label and left:
                self.temu_mappings.append({"label": label, "left_cell": left})

    def apdoroti_failus(self):
        katalogas = self.katalogo_kelias.get()
        if not katalogas:
            messagebox.showerror("Klaida", "Pirmiausia pasirinkite katalogą.")
            return

        self.nuskaityti_mappings_is_tree()
        self.rezultatai.clear()

        failai = [f for f in os.listdir(katalogas) if f.lower().endswith(".xlsx")]
        if not failai:
            messagebox.showinfo("Informacija", "Pasirinktame kataloge nerasta Excel failų.")
            return

        apdoroti = 0
        praleisti = []

        for failas in failai:
            kelias = os.path.join(katalogas, failas)
            try:
                wb = load_workbook(kelias, data_only=True)
            except (BadZipFile, InvalidFileException):
                praleisti.append(failas)
                continue

            ws = wb.active
            vardas = (ws["D4"].value or "").strip().title()
            pavarde = (ws["J4"].value or "").strip().title()
            pilnas_vardas = f"{vardas} {pavarde}".strip()
            klase = f""

            rezultatas = {"Vardas": vardas, "Pavardė": pavarde, "Klasė": ""}
            for tema in self.temu_mappings:
                tema_pavadinimas = tema["label"]
                langelis = tema["left_cell"]
                reikšmė = ws[langelis].value
                rezultatas[tema_pavadinimas] = reikšmė

            self.rezultatai[pilnas_vardas] = rezultatas
            apdoroti += 1

        zinute = f"Sėkmingai apdorota {apdoroti} failų."
        if praleisti:
            zinute += f"\nPraleisti failai ({len(praleisti)}):\n" + "\n".join(praleisti)
        messagebox.showinfo("Baigta", zinute)


    def eksportuoti_duomenis(self):
        if not self.rezultatai:
            messagebox.showinfo("Informacija", "Nėra duomenų eksportavimui.")
            return

        duomenys = list(self.rezultatai.values())
        df = pd.DataFrame(duomenys)

        df = df.sort_values(by=["Pavardė", "Vardas"])
        df.insert(0, "Numeris", range(1, len(df) + 1))

        failas = filedialog.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel failai", "*.xlsx")])
        if not failas:
            return

        try:
            df.to_excel(failas, index=False)
            messagebox.showinfo("Sėkmė", f"Duomenys sėkmingai eksportuoti į:\n{failas}")
        except Exception as e:
            messagebox.showerror("Klaida", f"Nepavyko eksportuoti: {e}")


if __name__ == "__main__":
    root = tk.Tk()
    app = ExcelProcessorApp(root)
    root.mainloop()

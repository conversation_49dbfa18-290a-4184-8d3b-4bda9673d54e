import os
import json
import threading
import customtkinter as ctk
from tkinter import filedialog, messagebox
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pandas as pd
from openpyxl import load_workbook, Workbook
from openpyxl.utils.exceptions import InvalidFileException
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from zipfile import BadZipFile
import logging
from datetime import datetime
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set appearance mode and color theme
ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"


class ConfigManager:
    """Manages application configuration"""
    def __init__(self, config_file: str = "excel_processor_config.json"):
        self.config_file = config_file
        self.default_config = {
            "themes": [
                {"label": "Etika", "display_name": "Etika", "left_cell": "N9", "right_cell": ""},
                {"label": "Tikyba", "display_name": "Tikyba", "left_cell": "N10", "right_cell": ""},
                {"label": "Lietuvių kalba", "display_name": "Lietuvių kalba ir literatūra", "left_cell": "N11", "right_cell": ""},
                {"label": "Matematika", "display_name": "Matematika", "left_cell": "N12", "right_cell": ""},
                {"label": "Fizinis", "display_name": "Fizinis ugdymas", "left_cell": "N13", "right_cell": ""},
                {"label": "Dailė", "display_name": "Dailė", "left_cell": "N14", "right_cell": ""},
                {"label": "Muzika", "display_name": "Muzika", "left_cell": "N15", "right_cell": ""},
                {"label": "Šokis", "display_name": "Šokis", "left_cell": "N16", "right_cell": ""},
                {"label": "Technologijos", "display_name": "Taikomosios technologijos", "left_cell": "N18", "right_cell": ""},
                {"label": "Anglų", "display_name": "Užsienio kalba (anglų) (1-oji) + modulis", "left_cell": "N21", "right_cell": ""},
                {"label": "Biologija", "display_name": "Biologija", "left_cell": "N22", "right_cell": ""},
                {"label": "Chemija", "display_name": "Chemija", "left_cell": "N23", "right_cell": ""},
                {"label": "Fizika", "display_name": "Fizika", "left_cell": "N24", "right_cell": ""},
                {"label": "Informatika", "display_name": "Informatika + modulis", "left_cell": "N25", "right_cell": ""},
                {"label": "Istorija", "display_name": "Istorija", "left_cell": "N26", "right_cell": ""},
                {"label": "Geografija", "display_name": "Geografija", "left_cell": "N27", "right_cell": ""},
                {"label": "Ekonomika", "display_name": "Ekonomika ir verslumas", "left_cell": "N28", "right_cell": ""},
                {"label": "Ispanų", "display_name": "Užsienio kalba (ispanų)", "left_cell": "H31", "right_cell": ""},
                {"label": "Vokiečių", "display_name": "Užsienio kalba (vokiečių)", "left_cell": "P31", "right_cell": ""},
                {"label": "Prancūzų", "display_name": "Užsienio kalba (prancūzų)", "left_cell": "G32", "right_cell": ""},
                {"label": "MBiologija", "display_name": "Biologija", "left_cell": "G35", "right_cell": ""},
                {"label": "MChemija", "display_name": "Chemija", "left_cell": "P35", "right_cell": ""},
                {"label": "MFizika", "display_name": "Fizika", "left_cell": "G36", "right_cell": ""},
                {"label": "MGeografija", "display_name": "Geografija", "left_cell": "P36", "right_cell": ""},
                {"label": "MIstorija", "display_name": "Istorija", "left_cell": "G37", "right_cell": ""},
                {"label": "MLietuvių", "display_name": "Lietuvių", "left_cell": "P37", "right_cell": ""},
            ],
            "last_directories": [],
            "name_cells": {"first_name": "D4", "last_name": "J4"},
            "class_cell": "",
            "max_workers": 4,
            "auto_save": True
        }

    def load(self) -> dict:
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # Merge with defaults to ensure all keys exist
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                # Ensure all themes have display_name
                for theme in config["themes"]:
                    if "display_name" not in theme:
                        theme["display_name"] = theme["label"]
                return config
        except (FileNotFoundError, json.JSONDecodeError):
            return self.default_config.copy()

    def save(self, config: dict):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save config: {e}")


class ExcelProcessor:
    """Handles Excel file processing logic with performance optimizations"""
    def __init__(self, config: dict):
        self.config = config
        self.results_by_folder = defaultdict(dict)
        self.all_results = {}
        self.errors = []
        self._cache = {}  # Cache for processed files

    def process_file(self, file_path: str, folder_name: str) -> Tuple[Optional[str], Optional[dict], Optional[str]]:
        """Process a single Excel file with caching and optimizations"""
        # Check cache first
        file_stat = os.path.getmtime(file_path)
        cache_key = f"{file_path}_{file_stat}"

        if cache_key in self._cache:
            return self._cache[cache_key]

        try:
            # Use read_only and data_only for better performance
            wb = load_workbook(file_path, data_only=True, read_only=True)
            ws = wb.active

            # Pre-fetch all needed cells in one go for better performance
            name_cells = self.config["name_cells"]
            cells_to_read = [name_cells["first_name"], name_cells["last_name"]]

            if self.config.get("class_cell"):
                cells_to_read.append(self.config["class_cell"])

            # Add theme cells
            for theme in self.config["themes"]:
                if theme["left_cell"]:
                    cells_to_read.append(theme["left_cell"])
                if theme.get("right_cell"):
                    cells_to_read.append(theme["right_cell"])

            # Read all cells at once
            cell_values = {}
            for cell_ref in cells_to_read:
                try:
                    cell_values[cell_ref] = ws[cell_ref].value
                except:
                    cell_values[cell_ref] = None

            # Extract student info
            first_name = str(cell_values.get(name_cells["first_name"]) or "").strip().title()
            last_name = str(cell_values.get(name_cells["last_name"]) or "").strip().title()
            full_name = f"{first_name} {last_name}".strip()

            # Get class if configured
            class_value = ""
            if self.config.get("class_cell"):
                class_value = str(cell_values.get(self.config["class_cell"]) or "").strip()

            result = {
                "Vardas": first_name,
                "Pavardė": last_name,
                "Klasė": class_value
            }

            # Extract theme values using display names
            for theme in self.config["themes"]:
                theme_display_name = theme.get("display_name", theme["label"])
                left_cell = theme["left_cell"]
                right_cell = theme.get("right_cell", "")

                value = None
                if left_cell:
                    value = cell_values.get(left_cell)
                if right_cell and value is None:
                    value = cell_values.get(right_cell)

                result[theme_display_name] = value

            wb.close()

            # Cache the result
            result_tuple = (full_name, result, None)
            self._cache[cache_key] = result_tuple
            return result_tuple

        except (BadZipFile, InvalidFileException):
            error_msg = f"Invalid file format: {os.path.basename(file_path)}"
            result_tuple = (None, None, error_msg)
            self._cache[cache_key] = result_tuple
            return result_tuple
        except Exception as e:
            error_msg = f"Error processing {os.path.basename(file_path)}: {str(e)}"
            result_tuple = (None, None, error_msg)
            self._cache[cache_key] = result_tuple
            return result_tuple

    def process_directories(self, directories: List[str], progress_callback=None) -> Tuple[Dict[str, dict], Dict[str, dict], List[str]]:
        """Process all Excel files in multiple directories"""
        self.results_by_folder.clear()
        self.all_results.clear()
        self.errors.clear()

        total_files = 0
        files_by_directory = {}

        # Count total files and organize by directory
        for directory in directories:
            excel_files = []
            for ext in ['.xlsx', '.xlsm']:
                excel_files.extend(Path(directory).glob(f"*{ext}"))
            files_by_directory[directory] = excel_files
            total_files += len(excel_files)

        if total_files == 0:
            return {}, {}, ["No Excel files found in selected directories"]

        processed = 0

        # Process each directory
        for directory, files in files_by_directory.items():
            folder_name = Path(directory).name

            # Process files in parallel for this directory
            with ThreadPoolExecutor(max_workers=self.config.get("max_workers", 4)) as executor:
                future_to_file = {
                    executor.submit(self.process_file, str(file), folder_name): file
                    for file in files
                }

                for future in as_completed(future_to_file):
                    full_name, result, error = future.result()

                    if error:
                        self.errors.append(f"[{folder_name}] {error}")
                    elif full_name and result:
                        # Add to folder-specific results
                        self.results_by_folder[folder_name][full_name] = result
                        # Add to all results
                        self.all_results[full_name] = result

                    processed += 1
                    if progress_callback:
                        progress_callback(processed, total_files)

        return self.results_by_folder, self.all_results, self.errors


class ModernExcelApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        # Configure window
        self.title("Excel Duomenų Apdorojimas - Modern Edition")
        self.geometry("1400x900")
        self.minsize(1200, 700)

        # Initialize components
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load()
        self.processor = ExcelProcessor(self.config)

        # Variables
        self.selected_directories = []
        self.processing = False

        # Create UI
        self.create_ui()
        self.load_last_directories()

        # Set window close handler
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_ui(self):
        """Create the modern user interface"""
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create sidebar
        self.create_sidebar()

        # Create main content area
        self.create_main_content()

    def create_sidebar(self):
        """Create modern sidebar navigation"""
        self.sidebar_frame = ctk.CTkFrame(self, width=200, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(4, weight=1)

        # Logo/Title
        self.logo_label = ctk.CTkLabel(self.sidebar_frame, text="Excel Processor",
                                      font=ctk.CTkFont(size=20, weight="bold"))
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        # Navigation buttons
        self.main_button = ctk.CTkButton(self.sidebar_frame, text="📁 Pagrindinis",
                                        command=lambda: self.show_frame("main"),
                                        height=40, font=ctk.CTkFont(size=14))
        self.main_button.grid(row=1, column=0, padx=20, pady=10, sticky="ew")

        self.config_button = ctk.CTkButton(self.sidebar_frame, text="⚙️ Konfigūracija",
                                          command=lambda: self.show_frame("config"),
                                          height=40, font=ctk.CTkFont(size=14))
        self.config_button.grid(row=2, column=0, padx=20, pady=10, sticky="ew")

        self.preview_button = ctk.CTkButton(self.sidebar_frame, text="📊 Rezultatai",
                                           command=lambda: self.show_frame("preview"),
                                           height=40, font=ctk.CTkFont(size=14))
        self.preview_button.grid(row=3, column=0, padx=20, pady=10, sticky="ew")

        # Status at bottom
        self.status_label = ctk.CTkLabel(self.sidebar_frame, text="✅ Pasiruošta",
                                        font=ctk.CTkFont(size=12))
        self.status_label.grid(row=5, column=0, padx=20, pady=(0, 20))

    def create_main_content(self):
        """Create main content area with frames"""
        # Main content frame
        self.main_content = ctk.CTkFrame(self)
        self.main_content.grid(row=0, column=1, sticky="nsew", padx=20, pady=20)
        self.main_content.grid_columnconfigure(0, weight=1)
        self.main_content.grid_rowconfigure(0, weight=1)

        # Create different frames for each section
        self.frames = {}

        # Main processing frame
        self.create_main_frame()

        # Configuration frame
        self.create_config_frame()

        # Preview frame
        self.create_preview_frame()

        # Show main frame by default
        self.show_frame("main")

    def create_main_frame(self):
        """Create main processing frame"""
        frame = ctk.CTkFrame(self.main_content)
        self.frames["main"] = frame

        # Title
        title = ctk.CTkLabel(frame, text="Excel Failų Apdorojimas",
                            font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=(20, 30))

        # Directory selection section
        dir_section = ctk.CTkFrame(frame)
        dir_section.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        dir_title = ctk.CTkLabel(dir_section, text="📁 Katalogų Pasirinkimas",
                                font=ctk.CTkFont(size=18, weight="bold"))
        dir_title.pack(pady=(20, 10))

        # Directory listbox
        self.dir_textbox = ctk.CTkTextbox(dir_section, height=150)
        self.dir_textbox.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Directory buttons
        dir_buttons = ctk.CTkFrame(dir_section)
        dir_buttons.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(dir_buttons, text="➕ Pridėti Katalogą",
                     command=self.add_directory, height=40).pack(side="left", padx=(0, 10))
        ctk.CTkButton(dir_buttons, text="🗑️ Išvalyti",
                     command=self.clear_directories, height=40).pack(side="left", padx=(0, 10))

        # Action buttons
        action_frame = ctk.CTkFrame(frame)
        action_frame.pack(fill="x", padx=20, pady=(0, 20))

        self.process_button = ctk.CTkButton(action_frame, text="🚀 Apdoroti Failus",
                                           command=self.process_files,
                                           height=50, font=ctk.CTkFont(size=16, weight="bold"))
        self.process_button.pack(side="left", padx=(0, 20), fill="x", expand=True)

        self.export_button = ctk.CTkButton(action_frame, text="📊 Eksportuoti",
                                          command=self.export_data,
                                          height=50, font=ctk.CTkFont(size=16, weight="bold"))
        self.export_button.pack(side="left", fill="x", expand=True)

        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(frame, height=20)
        self.progress_bar.pack(fill="x", padx=20, pady=(0, 20))
        self.progress_bar.set(0)

        # Results area
        results_frame = ctk.CTkFrame(frame)
        results_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        results_title = ctk.CTkLabel(results_frame, text="📋 Apdorojimo Rezultatai",
                                    font=ctk.CTkFont(size=18, weight="bold"))
        results_title.pack(pady=(20, 10))

        self.results_textbox = ctk.CTkTextbox(results_frame, height=200)
        self.results_textbox.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def show_frame(self, frame_name):
        """Show specific frame"""
        # Hide all frames
        for frame in self.frames.values():
            frame.pack_forget()

        # Show selected frame
        if frame_name in self.frames:
            self.frames[frame_name].pack(fill="both", expand=True)

        # Update button states
        self.update_button_states(frame_name)

    def update_button_states(self, active_frame):
        """Update navigation button states"""
        buttons = {
            "main": self.main_button,
            "config": self.config_button,
            "preview": self.preview_button
        }

        for name, button in buttons.items():
            if name == active_frame:
                button.configure(fg_color=("gray75", "gray25"))
            else:
                button.configure(fg_color=("gray85", "gray15"))

    def create_config_frame(self):
        """Create configuration frame"""
        frame = ctk.CTkFrame(self.main_content)
        self.frames["config"] = frame

        # Title
        title = ctk.CTkLabel(frame, text="Konfigūracijos Nustatymai",
                            font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=(20, 30))

        # Scrollable frame for themes
        themes_frame = ctk.CTkScrollableFrame(frame, label_text="📋 Temų Koordinatės")
        themes_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Theme configuration will be added here
        self.create_theme_config(themes_frame)

        # Settings section
        settings_frame = ctk.CTkFrame(frame)
        settings_frame.pack(fill="x", padx=20, pady=(0, 20))

        settings_title = ctk.CTkLabel(settings_frame, text="⚙️ Papildomi Nustatymai",
                                     font=ctk.CTkFont(size=18, weight="bold"))
        settings_title.pack(pady=(20, 10))

        # Settings grid
        settings_grid = ctk.CTkFrame(settings_frame)
        settings_grid.pack(fill="x", padx=20, pady=(0, 20))

        # Name cells
        ctk.CTkLabel(settings_grid, text="Vardo langelis:").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        self.name_cell_entry = ctk.CTkEntry(settings_grid, width=100)
        self.name_cell_entry.grid(row=0, column=1, padx=10, pady=10)
        self.name_cell_entry.insert(0, self.config["name_cells"]["first_name"])

        ctk.CTkLabel(settings_grid, text="Pavardės langelis:").grid(row=0, column=2, padx=10, pady=10, sticky="w")
        self.surname_cell_entry = ctk.CTkEntry(settings_grid, width=100)
        self.surname_cell_entry.grid(row=0, column=3, padx=10, pady=10)
        self.surname_cell_entry.insert(0, self.config["name_cells"]["last_name"])

        ctk.CTkLabel(settings_grid, text="Klasės langelis:").grid(row=1, column=0, padx=10, pady=10, sticky="w")
        self.class_cell_entry = ctk.CTkEntry(settings_grid, width=100)
        self.class_cell_entry.grid(row=1, column=1, padx=10, pady=10)
        self.class_cell_entry.insert(0, self.config.get("class_cell", ""))

        ctk.CTkLabel(settings_grid, text="Lygiagrečių procesų:").grid(row=1, column=2, padx=10, pady=10, sticky="w")
        self.workers_entry = ctk.CTkEntry(settings_grid, width=100)
        self.workers_entry.grid(row=1, column=3, padx=10, pady=10)
        self.workers_entry.insert(0, str(self.config.get("max_workers", 4)))

        # Save button
        save_button = ctk.CTkButton(settings_frame, text="💾 Išsaugoti Konfigūraciją",
                                   command=self.save_config, height=40)
        save_button.pack(pady=20)

    def create_theme_config(self, parent):
        """Create theme configuration widgets"""
        self.theme_widgets = []

        # Header
        header_frame = ctk.CTkFrame(parent)
        header_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(header_frame, text="Vidinis pavadinimas", width=150).grid(row=0, column=0, padx=5, pady=5)
        ctk.CTkLabel(header_frame, text="Rodomas pavadinimas", width=250).grid(row=0, column=1, padx=5, pady=5)
        ctk.CTkLabel(header_frame, text="Kairysis langelis", width=120).grid(row=0, column=2, padx=5, pady=5)
        ctk.CTkLabel(header_frame, text="Dešinysis langelis", width=120).grid(row=0, column=3, padx=5, pady=5)

        # Theme entries
        for i, theme in enumerate(self.config["themes"]):
            theme_frame = ctk.CTkFrame(parent)
            theme_frame.pack(fill="x", padx=10, pady=2)

            label_entry = ctk.CTkEntry(theme_frame, width=150)
            label_entry.grid(row=0, column=0, padx=5, pady=5)
            label_entry.insert(0, theme["label"])

            display_entry = ctk.CTkEntry(theme_frame, width=250)
            display_entry.grid(row=0, column=1, padx=5, pady=5)
            display_entry.insert(0, theme.get("display_name", theme["label"]))

            left_entry = ctk.CTkEntry(theme_frame, width=120)
            left_entry.grid(row=0, column=2, padx=5, pady=5)
            left_entry.insert(0, theme["left_cell"])

            right_entry = ctk.CTkEntry(theme_frame, width=120)
            right_entry.grid(row=0, column=3, padx=5, pady=5)
            right_entry.insert(0, theme.get("right_cell", ""))

            self.theme_widgets.append({
                "label": label_entry,
                "display": display_entry,
                "left": left_entry,
                "right": right_entry
            })

    def create_preview_frame(self):
        """Create preview frame"""
        frame = ctk.CTkFrame(self.main_content)
        self.frames["preview"] = frame

        # Title
        title = ctk.CTkLabel(frame, text="Rezultatų Peržiūra",
                            font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=(20, 30))

        # Folder selection
        folder_frame = ctk.CTkFrame(frame)
        folder_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkLabel(folder_frame, text="📁 Peržiūrėti katalogą:",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(side="left", padx=20, pady=20)

        self.folder_combobox = ctk.CTkComboBox(folder_frame, values=["Visi"],
                                              command=self.on_folder_changed, width=300)
        self.folder_combobox.pack(side="left", padx=(0, 20), pady=20)

        # Results display
        results_frame = ctk.CTkFrame(frame)
        results_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        self.preview_textbox = ctk.CTkTextbox(results_frame, height=400)
        self.preview_textbox.pack(fill="both", expand=True, padx=20, pady=20)

        # Export selected button
        export_frame = ctk.CTkFrame(frame)
        export_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(export_frame, text="📊 Eksportuoti Pasirinktus",
                     command=self.export_selected, height=40).pack(pady=20)

    def add_directory(self):
        """Add directory to the list"""
        directory = filedialog.askdirectory()
        if directory and directory not in self.selected_directories:
            self.selected_directories.append(directory)
            self.update_directory_display()

    def clear_directories(self):
        """Clear all directories"""
        self.selected_directories.clear()
        self.update_directory_display()

    def update_directory_display(self):
        """Update directory display"""
        self.dir_textbox.delete("0.0", "end")
        for directory in self.selected_directories:
            self.dir_textbox.insert("end", f"📁 {directory}\n")

    def process_files(self):
        """Process Excel files"""
        if not self.selected_directories:
            messagebox.showerror("Klaida", "Pasirinkite bent vieną katalogą.")
            return

        if self.processing:
            messagebox.showinfo("Informacija", "Apdorojimas jau vyksta.")
            return

        self.processing = True
        self.process_button.configure(text="🔄 Apdorojama...", state="disabled")
        self.progress_bar.set(0)
        self.results_textbox.delete("0.0", "end")

        def progress_callback(current, total):
            progress = current / total if total > 0 else 0
            self.after(0, lambda: self.progress_bar.set(progress))
            self.after(0, lambda: self.update_status(f"📊 Apdorota {current}/{total} failų"))

        def process_thread():
            try:
                start_time = time.time()
                results_by_folder, all_results, errors = self.processor.process_directories(
                    self.selected_directories, progress_callback
                )
                end_time = time.time()
                processing_time = end_time - start_time

                self.after(0, lambda: self.process_complete(results_by_folder, all_results, errors, processing_time))
            except Exception as e:
                self.after(0, lambda: self.process_error(str(e)))

        thread = threading.Thread(target=process_thread, daemon=True)
        thread.start()

    def process_complete(self, results_by_folder, all_results, errors, processing_time):
        """Handle processing completion"""
        self.processing = False
        self.process_button.configure(text="🚀 Apdoroti Failus", state="normal")

        # Display results
        self.results_textbox.delete("0.0", "end")

        total_files = len(all_results)
        files_per_second = total_files / processing_time if processing_time > 0 else 0

        results_text = f"📊 APDOROJIMO SUVESTINĖ\n"
        results_text += f"{'='*50}\n\n"
        results_text += f"⚡ Apdorojimo laikas: {processing_time:.2f} sek.\n"
        results_text += f"🚀 Greitis: {files_per_second:.1f} failų/sek.\n\n"
        results_text += f"📁 Iš viso apdorota failų: {total_files}\n"
        results_text += f"📂 Katalogų: {len(results_by_folder)}\n"
        results_text += f"❌ Klaidų: {len(errors)}\n\n"

        if results_by_folder:
            results_text += "📋 REZULTATAI PAGAL KATALOGUS:\n"
            results_text += f"{'-'*40}\n"
            for folder, results in sorted(results_by_folder.items()):
                status_icon = "✅" if results else "⚠️"
                results_text += f"{status_icon} {folder}: {len(results)} failų\n"

        if errors:
            results_text += f"\n🚨 KLAIDOS ({len(errors)}):\n"
            results_text += f"{'-'*30}\n"
            for error in errors[:10]:
                results_text += f"• {error}\n"
            if len(errors) > 10:
                results_text += f"... ir dar {len(errors) - 10} klaidų\n"

        self.results_textbox.insert("0.0", results_text)

        # Update preview
        folder_names = ["Visi"] + list(results_by_folder.keys())
        self.folder_combobox.configure(values=folder_names)
        if folder_names:
            self.folder_combobox.set(folder_names[0])

        if all_results:
            messagebox.showinfo("🎉 Baigta", f"✅ Sėkmingai apdorota {len(all_results)} failų per {processing_time:.1f} sek.")
            self.update_status(f"✅ Baigta: {len(all_results)} failų apdorota")
        else:
            self.update_status("⚠️ Nerastas nei vienas tinkamas failas")

    def process_error(self, error_msg):
        """Handle processing errors"""
        self.processing = False
        self.process_button.configure(text="🚀 Apdoroti Failus", state="normal")
        messagebox.showerror("❌ Klaida", f"Apdorojimo klaida: {error_msg}")
        self.update_status("❌ Apdorojimas nutrauktas dėl klaidos")

    def update_status(self, message):
        """Update status label"""
        self.status_label.configure(text=message)

    def save_config(self):
        """Save configuration"""
        # Update config from UI
        self.config["name_cells"]["first_name"] = self.name_cell_entry.get()
        self.config["name_cells"]["last_name"] = self.surname_cell_entry.get()
        self.config["class_cell"] = self.class_cell_entry.get()
        try:
            self.config["max_workers"] = int(self.workers_entry.get())
        except ValueError:
            self.config["max_workers"] = 4

        # Update themes
        self.config["themes"] = []
        for widget_set in self.theme_widgets:
            label = widget_set["label"].get()
            display = widget_set["display"].get()
            left = widget_set["left"].get()
            right = widget_set["right"].get()

            if label and left:
                self.config["themes"].append({
                    "label": label,
                    "display_name": display or label,
                    "left_cell": left,
                    "right_cell": right
                })

        self.config["last_directories"] = self.selected_directories
        self.config_manager.save(self.config)
        self.processor.config = self.config
        messagebox.showinfo("Sėkmė", "Konfigūracija išsaugota!")

    def load_last_directories(self):
        """Load last used directories"""
        if self.config.get("last_directories"):
            self.selected_directories = self.config["last_directories"]
            self.update_directory_display()

    def on_folder_changed(self, choice):
        """Handle folder selection change"""
        # Update preview based on selected folder
        pass

    def export_data(self):
        """Export all data to Excel"""
        if not self.processor.all_results:
            messagebox.showinfo("Informacija", "Nėra duomenų eksportavimui.")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel failai", "*.xlsx")],
            initialfile=f"planai_{datetime.now().strftime('%Y_%m_%d_%H%M%S')}.xlsx"
        )

        if file_path:
            try:
                # Create workbook and export (simplified version)
                wb = Workbook()
                ws = wb.active
                ws.title = "Bendras"

                # Add headers
                headers = ["Nr.", "Vardas", "Pavardė", "Klasė"]
                for theme in self.config["themes"]:
                    headers.append(theme.get("display_name", theme["label"]))
                headers.extend(["Suma", "Parašas"])

                for col, header in enumerate(headers, 1):
                    ws.cell(row=1, column=col, value=header)

                # Add data
                for row, (name, data) in enumerate(self.processor.all_results.items(), 2):
                    ws.cell(row=row, column=1, value=row-1)
                    ws.cell(row=row, column=2, value=data.get("Vardas", ""))
                    ws.cell(row=row, column=3, value=data.get("Pavardė", ""))
                    ws.cell(row=row, column=4, value=data.get("Klasė", ""))

                wb.save(file_path)
                messagebox.showinfo("Sėkmė", f"Duomenys eksportuoti į:\n{file_path}")
            except Exception as e:
                messagebox.showerror("Klaida", f"Nepavyko eksportuoti: {str(e)}")

    def export_selected(self):
        """Export selected data"""
        messagebox.showinfo("Informacija", "Funkcija bus įgyvendinta ateityje.")

    def on_closing(self):
        """Handle window closing"""
        if self.processing:
            if messagebox.askokcancel("Uždaryti", "Apdorojimas dar vyksta. Ar tikrai norite uždaryti?"):
                self.destroy()
        else:
            self.save_config()
            self.destroy()


def main():
    """Main application entry point"""
    app = ModernExcelApp()
    app.mainloop()


if __name__ == "__main__":
    main()

import tkinter as tk
from tkinter import filedialog
from PIL import Image, ImageTk
from datetime import datetime
import comtypes.client

class PhotoEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("Photo Editor")

        # Variables to store user input
        self.name_var = tk.StringVar()
        self.price_var = tk.StringVar()
        self.reason_var = tk.StringVar()
        self.image_path = None

        # Create GUI elements
        tk.Label(root, text="Name:").pack()
        self.name_entry = tk.Entry(root, textvariable=self.name_var)
        self.name_entry.pack()

        tk.Label(root, text="Price:").pack()
        self.price_entry = tk.Entry(root, textvariable=self.price_var)
        self.price_entry.pack()

        tk.Label(root, text="Reason:").pack()
        self.reason_entry = tk.Entry(root, textvariable=self.reason_var)
        self.reason_entry.pack()

        tk.Button(root, text="Choose Image", command=self.choose_image).pack()

        self.image_label = tk.Label(root)
        self.image_label.pack()

        tk.Button(root, text="Generate PDF", command=self.generate_pdf).pack()

    def choose_image(self):
        file_path = filedialog.askopenfilename(title="Choose Image", filetypes=[("Image files", "*.png;*.jpg;*.jpeg")])
        if file_path:
            self.image_path = file_path
            self.display_image(file_path)

    def display_image(self, file_path):
        image = Image.open(file_path)
        image.thumbnail((200, 200))
        photo = ImageTk.PhotoImage(image)
        self.image_label.config(image=photo)
        self.image_label.image = photo

    def generate_pdf(self):
        if not self.image_path:
            return

        # Create Photoshop COM object
        ps_app = comtypes.client.CreateObject("Photoshop.Application")

        # Open the PSD template
        psd_template = ps_app.Open("C:/Users/<USER>/Desktop/photo.psd")

        # Update text layers
        for layer in psd_template.ArtLayers:
            if layer.Name == "Name":
                layer.TextItem.Contents = self.name_var.get()
            elif layer.Name == "Bounty":
                layer.TextItem.Contents = self.price_var.get()
            elif layer.Name == "Reason":
                layer.TextItem.Contents = self.reason_var.get()

        # Replace image layer
        image = Image.open(self.image_path)
        image = image.convert("RGB")
        image.save("temp.jpg")  # Save the image to a temporary file
        psd_template.ArtLayers["Photo"].Place("temp.jpg")

        # Save the edited PSD as PDF
        pdf_filename = f"output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        psd_template.SaveAs(pdf_filename, 2)  # 2 corresponds to PDF format
        psd_template.Close()

if __name__ == "__main__":
    root = tk.Tk()
    app = PhotoEditor(root)
    root.mainloop()
